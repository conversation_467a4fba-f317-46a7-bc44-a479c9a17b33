// Central exports for all core types
export * from './entities';
export * from './api';
export * from './ui';

// Re-export commonly used types from existing files for backward compatibility
export type { 
  ApiResponse, 
  PaginatedResponse, 
  BaseListParams, 
  BaseFilterParams 
} from './api';

export type {
  BaseEntity,
  User,
  Admin,
  Product,
  Category,
  Brand,
  Order,
  Cart,
  Media,
  // Enums
  Gender,
  AdminRole,
  ProductStatus,
  AttributeType,
  OrderStatus,
  PaymentStatus,
  PaymentMethod,
  MediaType,
  NotificationType,
  AuditAction,
  AdminPermission
} from './entities';

export type {
  BaseComponentProps,
  ButtonProps,
  InputProps,
  SelectProps,
  TableProps,
  TableColumn,
  ModalProps,
  FormProps,
  AdminLayoutProps,
  NavigationItem
} from './ui';