// API request/response types - Centralized API interfaces

// Base response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string | any;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
}

// Base request parameters
export interface BaseListParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BaseFilterParams {
  startDate?: string;
  endDate?: string;
  status?: string;
  isActive?: boolean;
}

// Product API types
export interface ProductListParams extends BaseListParams, BaseFilterParams {
  categoryId?: string;
  brandId?: string;
  minPrice?: number;
  maxPrice?: number;
  featured?: boolean;
  tags?: string[];
  attributes?: Record<string, string[]>;
}

export interface ProductCreateRequest {
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  status: string;
  featured?: boolean;
  weight?: number;
  dimensions?: string;
  tags?: string[];
  metaTitle?: string;
  metaDescription?: string;
  categoryId?: string;
  brandId?: string;
  images?: Array<{
    mediaId: string;
    sortOrder: number;
    isMain?: boolean;
  }>;
  attributes?: Array<{
    attributeId: string;
    attributeValueId?: string;
    customValue?: string;
  }>;
}

export interface ProductUpdateRequest extends Partial<ProductCreateRequest> {}

// Category API types
export interface CategoryListParams extends BaseListParams {
  parentId?: string;
  includeChildren?: boolean;
}

export interface CategoryCreateRequest {
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  isActive?: boolean;
  sortOrder?: number;
  metaTitle?: string;
  metaDescription?: string;
}

export interface CategoryUpdateRequest extends Partial<CategoryCreateRequest> {}

// Order API types
export interface OrderListParams extends BaseListParams, BaseFilterParams {
  userId?: string;
  paymentStatus?: string;
  shippingMethod?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface OrderCreateRequest {
  userId: string;
  items: Array<{
    productId: string;
    quantity: number;
    unitPrice: number;
  }>;
  shippingAddress: {
    firstName: string;
    lastName: string;
    company?: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phone?: string;
  };
  billingAddress?: {
    firstName: string;
    lastName: string;
    company?: string;
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
    phone?: string;
  };
  paymentMethod?: string;
  shippingMethod?: string;
  notes?: string;
}

export interface OrderUpdateRequest {
  status?: string;
  paymentStatus?: string;
  shippingMethod?: string;
  notes?: string;
}

// User API types
export interface UserListParams extends BaseListParams, BaseFilterParams {
  hasOrders?: boolean;
  registrationDate?: string;
}

export interface UserCreateRequest {
  email: string;
  name: string;
  password: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
}

export interface UserUpdateRequest extends Partial<Omit<UserCreateRequest, 'password'>> {
  isActive?: boolean;
}

// Auth API types
export interface LoginRequest {
  email: string;
  password: string;
  remember?: boolean;
}

export interface RegisterRequest {
  email: string;
  name: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Cart API types
export interface CartAddItemRequest {
  productId: string;
  quantity: number;
  attributes?: Record<string, string>;
}

export interface CartUpdateItemRequest {
  quantity: number;
}

// Wishlist API types
export interface WishlistAddItemRequest {
  productId: string;
}

// Review API types
export interface ReviewCreateRequest {
  productId: string;
  rating: number;
  title?: string;
  content?: string;
}

export interface ReviewUpdateRequest extends Partial<Omit<ReviewCreateRequest, 'productId'>> {}

export interface ReviewListParams extends BaseListParams {
  productId?: string;
  userId?: string;
  rating?: number;
  isApproved?: boolean;
}

// Media API types
export interface MediaUploadRequest {
  file: File;
  folder?: string;
  alt?: string;
  title?: string;
  description?: string;
}

export interface MediaListParams extends BaseListParams {
  type?: string;
  folder?: string;
  mimeType?: string;
}

export interface MediaUpdateRequest {
  alt?: string;
  title?: string;
  description?: string;
  folder?: string;
}

// Admin API types
export interface AdminCreateRequest {
  email: string;
  name: string;
  password: string;
  role: string;
  permissions: string[];
}

export interface AdminUpdateRequest extends Partial<Omit<AdminCreateRequest, 'password'>> {
  isActive?: boolean;
}

export interface AdminListParams extends BaseListParams {
  role?: string;
}

// Settings API types
export interface SettingUpdateRequest {
  value: string;
}

export interface SettingsListParams {
  category?: string;
  isPublic?: boolean;
}

// Analytics API types
export interface AnalyticsParams {
  startDate: string;
  endDate: string;
  granularity?: 'day' | 'week' | 'month';
}

export interface DashboardStatsResponse {
  orders: {
    total: number;
    pending: number;
    completed: number;
    revenue: number;
  };
  products: {
    total: number;
    active: number;
    outOfStock: number;
    lowStock: number;
  };
  users: {
    total: number;
    active: number;
    newThisMonth: number;
  };
  reviews: {
    total: number;
    pending: number;
    averageRating: number;
  };
}

// Search API types
export interface SearchParams {
  query: string;
  filters?: {
    categories?: string[];
    brands?: string[];
    priceRange?: {
      min: number;
      max: number;
    };
    attributes?: Record<string, string[]>;
    inStock?: boolean;
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
  page?: number;
  limit?: number;
}

export interface SearchResponse<T = any> {
  results: T[];
  total: number;
  facets: {
    categories: Array<{ id: string; name: string; count: number }>;
    brands: Array<{ id: string; name: string; count: number }>;
    priceRange: { min: number; max: number };
    attributes: Record<string, Array<{ value: string; count: number }>>;
  };
  suggestions?: string[];
}

// Bulk operations
export interface BulkOperationRequest<T = any> {
  action: 'create' | 'update' | 'delete';
  items: T[];
}

export interface BulkOperationResponse {
  success: number;
  failed: number;
  errors: Array<{
    index: number;
    error: string;
  }>;
}

// File export/import
export interface ExportRequest {
  format: 'csv' | 'xlsx' | 'json';
  filters?: Record<string, any>;
  fields?: string[];
}

export interface ImportRequest {
  file: File;
  mapping: Record<string, string>;
  options?: {
    skipHeader?: boolean;
    delimiter?: string;
    encoding?: string;
  };
}

// Notification API types
export interface NotificationListParams extends BaseListParams {
  isRead?: boolean;
  type?: string;
}

export interface NotificationUpdateRequest {
  isRead?: boolean;
}

// Audit log API types
export interface AuditLogListParams extends BaseListParams {
  userId?: string;
  adminId?: string;
  action?: string;
  entity?: string;
  entityId?: string;
  startDate?: string;
  endDate?: string;
}

// Error response types
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: ValidationError[];
  code?: string;
  stack?: string;
}