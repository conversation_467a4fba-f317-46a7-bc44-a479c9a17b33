// Core domain entities - Central type definitions
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// User & Authentication
export interface User extends BaseEntity {
  email: string;
  name: string;
  password: string;
  avatarId?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  isActive: boolean;
  avatar?: Media;
  orders?: Order[];
  addresses?: Address[];
  reviews?: Review[];
  cart?: Cart;
  wishlistItems?: WishlistItem[];
}

export interface Admin extends BaseEntity {
  email: string;
  name: string;
  password: string;
  role: AdminRole;
  permissions: AdminPermission[];
  isActive: boolean;
  lastLoginAt?: Date;
}

// Product & Catalog
export interface Product extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  status: ProductStatus;
  featured: boolean;
  weight?: number;
  dimensions?: string;
  tags: string[];
  metaTitle?: string;
  metaDescription?: string;
  categoryId?: string;
  brandId?: string;
  
  // Relations
  category?: Category;
  brand?: Brand;
  images?: ProductImage[];
  attributes?: ProductAttribute[];
  reviews?: Review[];
  cartItems?: CartItem[];
  orderItems?: OrderItem[];
  wishlistItems?: WishlistItem[];
  inventoryMovements?: InventoryMovement[];
}

export interface Category extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: string;
  isActive: boolean;
  sortOrder: number;
  metaTitle?: string;
  metaDescription?: string;
  
  // Relations
  parent?: Category;
  children?: Category[];
  products?: Product[];
}

export interface Brand extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  isActive: boolean;
  
  // Relations
  products?: Product[];
}

// Attributes & Variants
export interface Attribute extends BaseEntity {
  name: string;
  slug: string;
  type: AttributeType;
  description?: string;
  isRequired: boolean;
  isFilterable: boolean;
  sortOrder: number;
  
  // Relations
  values?: AttributeValue[];
  productAttributes?: ProductAttribute[];
}

export interface AttributeValue extends BaseEntity {
  value: string;
  slug: string;
  color?: string;
  image?: string;
  sortOrder: number;
  attributeId: string;
  
  // Relations
  attribute?: Attribute;
  productAttributes?: ProductAttribute[];
}

export interface ProductAttribute extends BaseEntity {
  productId: string;
  attributeId: string;
  attributeValueId?: string;
  customValue?: string;
  
  // Relations
  product?: Product;
  attribute?: Attribute;
  attributeValue?: AttributeValue;
}

// Orders & Commerce
export interface Order extends BaseEntity {
  orderNumber: string;
  userId: string;
  status: OrderStatus;
  totalAmount: number;
  subtotalAmount: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  currency: string;
  paymentStatus: PaymentStatus;
  paymentMethod?: string;
  shippingMethod?: string;
  notes?: string;
  
  // Address info
  shippingAddress: Address;
  billingAddress?: Address;
  
  // Relations
  user?: User;
  items?: OrderItem[];
  payments?: Payment[];
}

export interface OrderItem extends BaseEntity {
  orderId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  
  // Relations
  order?: Order;
  product?: Product;
}

export interface Cart extends BaseEntity {
  userId: string;
  
  // Relations
  user?: User;
  items?: CartItem[];
}

export interface CartItem extends BaseEntity {
  cartId: string;
  productId: string;
  quantity: number;
  
  // Relations
  cart?: Cart;
  product?: Product;
}

// Media & Content
export interface Media extends BaseEntity {
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  alt?: string;
  title?: string;
  description?: string;
  folder?: string;
  type: MediaType;
  isPublic: boolean;
}

export interface ProductImage extends BaseEntity {
  productId: string;
  mediaId: string;
  sortOrder: number;
  isMain: boolean;
  
  // Relations
  product?: Product;
  media?: Media;
}

// Reviews & Ratings
export interface Review extends BaseEntity {
  userId: string;
  productId: string;
  rating: number;
  title?: string;
  content?: string;
  isVerifiedPurchase: boolean;
  isApproved: boolean;
  helpfulCount: number;
  
  // Relations
  user?: User;
  product?: Product;
}

// Inventory
export interface InventoryMovement extends BaseEntity {
  productId: string;
  type: InventoryMovementType;
  quantity: number;
  previousStock: number;
  newStock: number;
  reason?: string;
  reference?: string;
  notes?: string;
  
  // Relations
  product?: Product;
}

// System & Configuration
export interface Setting extends BaseEntity {
  key: string;
  value: string;
  type: SettingType;
  category: string;
  description?: string;
  isPublic: boolean;
}

export interface Address extends BaseEntity {
  userId: string;
  type: AddressType;
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
  isDefault: boolean;
  
  // Relations
  user?: User;
}

export interface WishlistItem extends BaseEntity {
  userId: string;
  productId: string;
  
  // Relations
  user?: User;
  product?: Product;
}

export interface Payment extends BaseEntity {
  orderId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  gatewayResponse?: any;
  
  // Relations
  order?: Order;
}

// Notifications & Audit
export interface Notification extends BaseEntity {
  userId?: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  readAt?: Date;
  
  // Relations
  user?: User;
}

export interface AuditLog extends BaseEntity {
  userId?: string;
  adminId?: string;
  action: AuditAction;
  entity: string;
  entityId: string;
  oldValues?: any;
  newValues?: any;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
}

// Enums
export enum Gender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER'
}

export enum AdminRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  EDITOR = 'EDITOR',
  VIEWER = 'VIEWER'
}

export enum ProductStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  DISCONTINUED = 'DISCONTINUED'
}

export enum AttributeType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT',
  BOOLEAN = 'BOOLEAN',
  COLOR = 'COLOR',
  IMAGE = 'IMAGE'
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PROCESSING = 'PROCESSING',
  SHIPPED = 'SHIPPED',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',
  DEBIT_CARD = 'DEBIT_CARD',
  PAYPAL = 'PAYPAL',
  BANK_TRANSFER = 'BANK_TRANSFER',
  CASH_ON_DELIVERY = 'CASH_ON_DELIVERY',
  WALLET = 'WALLET'
}

export enum MediaType {
  IMAGE = 'IMAGE',
  VIDEO = 'VIDEO',
  DOCUMENT = 'DOCUMENT',
  AUDIO = 'AUDIO',
  OTHER = 'OTHER'
}

export enum InventoryMovementType {
  PURCHASE = 'PURCHASE',
  SALE = 'SALE',
  RETURN = 'RETURN',
  ADJUSTMENT = 'ADJUSTMENT',
  DAMAGE = 'DAMAGE',
  TRANSFER = 'TRANSFER'
}

export enum SettingType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  TEXT = 'TEXT'
}

export enum AddressType {
  SHIPPING = 'SHIPPING',
  BILLING = 'BILLING',
  BOTH = 'BOTH'
}

export enum NotificationType {
  ORDER = 'ORDER',
  PRODUCT = 'PRODUCT',
  PROMOTION = 'PROMOTION',
  SYSTEM = 'SYSTEM',
  REVIEW = 'REVIEW'
}

export enum AuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  VIEW = 'VIEW',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT'
}

export enum AdminPermission {
  // Product management
  PRODUCTS_VIEW = 'PRODUCTS_VIEW',
  PRODUCTS_CREATE = 'PRODUCTS_CREATE',
  PRODUCTS_UPDATE = 'PRODUCTS_UPDATE',
  PRODUCTS_DELETE = 'PRODUCTS_DELETE',
  
  // Category management
  CATEGORIES_VIEW = 'CATEGORIES_VIEW',
  CATEGORIES_CREATE = 'CATEGORIES_CREATE',
  CATEGORIES_UPDATE = 'CATEGORIES_UPDATE',
  CATEGORIES_DELETE = 'CATEGORIES_DELETE',
  
  // Order management
  ORDERS_VIEW = 'ORDERS_VIEW',
  ORDERS_UPDATE = 'ORDERS_UPDATE',
  ORDERS_DELETE = 'ORDERS_DELETE',
  
  // User management
  USERS_VIEW = 'USERS_VIEW',
  USERS_CREATE = 'USERS_CREATE',
  USERS_UPDATE = 'USERS_UPDATE',
  USERS_DELETE = 'USERS_DELETE',
  
  // Admin management
  ADMINS_VIEW = 'ADMINS_VIEW',
  ADMINS_CREATE = 'ADMINS_CREATE',
  ADMINS_UPDATE = 'ADMINS_UPDATE',
  ADMINS_DELETE = 'ADMINS_DELETE',
  
  // System management
  SETTINGS_VIEW = 'SETTINGS_VIEW',
  SETTINGS_UPDATE = 'SETTINGS_UPDATE',
  ANALYTICS_VIEW = 'ANALYTICS_VIEW',
  AUDIT_LOGS_VIEW = 'AUDIT_LOGS_VIEW'
}