// DEPRECATED: This file is deprecated. Please use @/types/core instead.
// This file is kept for backward compatibility but will be removed in future versions.

// Re-export everything from core types for backward compatibility
export * from './core';

// Legacy type mappings (DEPRECATED - use core types instead)
import type {
  User as CoreUser,
  Product as CoreProduct,
  Category as CoreCategory,
  Cart as CoreCart,
  Order as CoreOrder,
  Media as CoreMedia,
  ApiResponse as CoreApiResponse,
  PaginatedResponse as CorePaginatedResponse,
} from './core';

// Re-export with legacy names for backward compatibility
export type User = CoreUser;
export type Product = CoreProduct;
export type Category = CoreCategory;
export type Cart = CoreCart;
export type Order = CoreOrder;
export type Media = CoreMedia;
export type ApiResponse<T = any> = CoreApiResponse<T>;
export type PaginatedResponse<T> = CorePaginatedResponse<T>;

// Legacy interfaces that need to be maintained for existing code
export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
  total: number;
}

export interface Address {
  id: string;
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  isDefault: boolean;
}

// Legacy brand interface (use core Brand instead)
export interface Brand {
  id: string;
  name: string;
  description?: string;
  slug: string;
  logo?: {
    id: string;
    url: string;
    alt?: string;
    title?: string;
  } | null;
  website?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  products?: Product[];
}

// Legacy inventory types (to be moved to core)
export interface InventoryEntry {
  id: string;
  productId: string;
  product?: Product;
  quantity: number;
  reserved: number;
  available: number;
  minStock: number;
  maxStock?: number;
  location?: string;
  createdAt: Date;
  updatedAt: Date;
  stockMovements?: StockMovement[];
}

export interface StockMovement {
  id: string;
  inventoryEntryId: string;
  inventoryEntry?: InventoryEntry;
  type: "IN" | "OUT" | "TRANSFER" | "ADJUSTMENT";
  quantity: number;
  reason?: string;
  reference?: string;
  notes?: string;
  createdBy?: string;
  createdAt: Date;
}

export interface InventoryAlert {
  id: string;
  productId: string;
  product: Product;
  type: "LOW_STOCK" | "OUT_OF_STOCK" | "OVERSTOCK";
  message: string;
  threshold: number;
  currentStock: number;
  createdAt: Date;
}

export interface InventoryStats {
  totalProducts: number;
  totalStock: number;
  lowStockProducts: number;
  outOfStockProducts: number;
  totalValue: number;
  recentMovements: StockMovement[];
  alerts: InventoryAlert[];
}

// Legacy filter types (use core types instead)
export interface ProductFilters {
  categoryId?: string;
  brandId?: string;
  minPrice?: number;
  maxPrice?: number;
  tags?: string[];
  featured?: boolean;
  status?: Product["status"];
  search?: string;
}

export interface BrandFilters {
  search?: string;
  isActive?: boolean;
}

export interface InventoryFilters {
  search?: string;
  location?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  brandId?: string;
  categoryId?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

// Warning for developers
console.warn(
  '⚠️  WARNING: You are importing from @/types/index which is deprecated. ' +
  'Please migrate to @/types/core for better type safety and performance. ' +
  'This file will be removed in a future version.'
);