'use client';

import React, { ReactNode, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import { useCartStore } from '@/lib/stores/CartStore';
import { useUserStore } from '@/lib/stores/UserStore';
import { useSearchStore } from '@/lib/stores/SearchStore';

/**
 * Create a stable query client instance
 */
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if ((error as any)?.status >= 400 && (error as any)?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false,
    },
  },
});

/**
 * Store hydration component to sync stores with server state
 */
function StoreHydration({ children }: { children: ReactNode }) {
  const { data: session, status } = useSession();
  
  // Store actions
  const fetchCart = useCartStore(state => state.fetchCart);
  const fetchUser = useUserStore(state => state.fetchUser);
  const setUser = useUserStore(state => state.setUser);
  const fetchPopularSearches = useSearchStore(state => state.fetchPopularSearches);

  // Hydrate stores when session is available
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      // Set user from session
      setUser(session.user as any);
      
      // Fetch additional user data
      fetchUser();
      
      // Fetch user's cart
      fetchCart();
    } else if (status === 'unauthenticated') {
      // Clear user data
      setUser(null);
    }
  }, [session, status, setUser, fetchUser, fetchCart]);

  // Fetch popular searches on mount
  useEffect(() => {
    fetchPopularSearches();
  }, [fetchPopularSearches]);

  // Auto-sync cart periodically for authenticated users
  useEffect(() => {
    if (status === 'authenticated') {
      const interval = setInterval(() => {
        fetchCart();
      }, 5 * 60 * 1000); // Sync every 5 minutes

      return () => clearInterval(interval);
    }
  }, [status, fetchCart]);

  return <>{children}</>;
}

/**
 * Store performance monitor
 */
function StorePerformanceMonitor() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Monitor store performance in development
      const cartStore = useCartStore.getState();
      const userStore = useUserStore.getState();
      const searchStore = useSearchStore.getState();

      // Log store statistics
      console.log('🏪 Store Performance Monitor Initialized');
      
      // Monitor cart store
      const unsubscribeCart = useCartStore.subscribe((state, prevState) => {
        if (state.loading !== prevState.loading) {
          console.log(`🛒 Cart loading: ${state.loading}`);
        }
        if (state.error !== prevState.error && state.error) {
          console.error(`🛒 Cart error: ${state.error}`);
        }
      });

      // Monitor user store
      const unsubscribeUser = useUserStore.subscribe((state, prevState) => {
        if (state.loading !== prevState.loading) {
          console.log(`👤 User loading: ${state.loading}`);
        }
        if (state.error !== prevState.error && state.error) {
          console.error(`👤 User error: ${state.error}`);
        }
      });

      // Monitor search store
      const unsubscribeSearch = useSearchStore.subscribe((state, prevState) => {
        if (state.loading !== prevState.loading) {
          console.log(`🔍 Search loading: ${state.loading}`);
        }
        if (state.error !== prevState.error && state.error) {
          console.error(`🔍 Search error: ${state.error}`);
        }
      });

      return () => {
        unsubscribeCart();
        unsubscribeUser();
        unsubscribeSearch();
      };
    }
  }, []);

  return null;
}

/**
 * Store error boundary
 */
class StoreErrorBoundary extends React.Component<
  { children: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 Store Error Boundary caught an error:', error, errorInfo);
    
    // Reset stores to prevent cascading errors
    try {
      useCartStore.getState().reset();
      useUserStore.getState().reset();
      useSearchStore.getState().clearSearch();
    } catch (resetError) {
      console.error('Failed to reset stores:', resetError);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-red-50">
          <div className="text-center p-8">
            <h1 className="text-2xl font-bold text-red-600 mb-4">
              Something went wrong with the store
            </h1>
            <p className="text-red-500 mb-4">
              {this.state.error?.message || 'An unexpected error occurred'}
            </p>
            <button
              onClick={() => {
                this.setState({ hasError: false, error: undefined });
                window.location.reload();
              }}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Store cleanup on page unload
 */
function StoreCleanup() {
  useEffect(() => {
    const handleBeforeUnload = () => {
      // Clear sensitive data before page unload
      const userStore = useUserStore.getState();
      if (userStore.sessionData) {
        userStore.updateLastActivity();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return null;
}

/**
 * Main store provider component
 */
interface StoreProviderProps {
  children: ReactNode;
}

export function StoreProvider({ children }: StoreProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <StoreErrorBoundary>
        <StoreHydration>
          <StorePerformanceMonitor />
          <StoreCleanup />
          {children}
        </StoreHydration>
      </StoreErrorBoundary>
    </QueryClientProvider>
  );
}

/**
 * Store development tools
 */
export function StoreDevTools() {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
      // Add stores to window for debugging
      (window as any).__STORES__ = {
        cart: useCartStore,
        user: useUserStore,
        search: useSearchStore,
      };

      // Add helper functions
      (window as any).__STORE_HELPERS__ = {
        resetAllStores: () => {
          useCartStore.getState().reset();
          useUserStore.getState().reset();
          useSearchStore.getState().clearSearch();
          console.log('🔄 All stores reset');
        },
        logStoreStates: () => {
          console.log('🏪 Store States:');
          console.log('Cart:', useCartStore.getState());
          console.log('User:', useUserStore.getState());
          console.log('Search:', useSearchStore.getState());
        },
        exportStoreStates: () => {
          const states = {
            cart: useCartStore.getState(),
            user: useUserStore.getState(),
            search: useSearchStore.getState(),
          };
          const dataStr = JSON.stringify(states, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `store-states-${Date.now()}.json`;
          link.click();
          URL.revokeObjectURL(url);
        },
      };

      console.log('🛠️ Store Dev Tools loaded. Available commands:');
      console.log('- __STORES__: Access store instances');
      console.log('- __STORE_HELPERS__.resetAllStores(): Reset all stores');
      console.log('- __STORE_HELPERS__.logStoreStates(): Log current store states');
      console.log('- __STORE_HELPERS__.exportStoreStates(): Export store states as JSON');
    }
  }, []);

  return null;
}

/**
 * Store persistence manager
 */
export function StorePersistenceManager() {
  useEffect(() => {
    // Handle storage events for cross-tab synchronization
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key?.startsWith('user-store')) {
        // Sync user store across tabs
        const userStore = useUserStore.getState();
        userStore.refreshUser();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Periodic persistence cleanup
  useEffect(() => {
    const cleanup = () => {
      try {
        // Clean up expired cache entries
        const cacheKeys = Object.keys(localStorage).filter(key => 
          key.includes('-cache-') || key.includes('-temp-')
        );
        
        cacheKeys.forEach(key => {
          try {
            const item = JSON.parse(localStorage.getItem(key) || '{}');
            if (item.expiry && Date.now() > item.expiry) {
              localStorage.removeItem(key);
            }
          } catch {
            // Remove invalid cache entries
            localStorage.removeItem(key);
          }
        });
      } catch (error) {
        console.warn('Failed to cleanup storage:', error);
      }
    };

    // Run cleanup on mount and every hour
    cleanup();
    const interval = setInterval(cleanup, 60 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, []);

  return null;
}

/**
 * Complete store provider with all features
 */
export function ComprehensiveStoreProvider({ children }: StoreProviderProps) {
  return (
    <StoreProvider>
      {process.env.NODE_ENV === 'development' && <StoreDevTools />}
      <StorePersistenceManager />
      {children}
    </StoreProvider>
  );
}

export default StoreProvider;