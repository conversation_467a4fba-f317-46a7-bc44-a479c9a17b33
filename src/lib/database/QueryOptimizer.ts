import { Prisma } from '@prisma/client';
import { prisma } from '@/lib/prisma';

/**
 * Database query optimizer with intelligent caching and indexing strategies
 */
export class QueryOptimizer {
  private static instance: QueryOptimizer;
  private queryCache = new Map<string, any>();
  private performanceMetrics = new Map<string, QueryMetrics>();

  static getInstance(): QueryOptimizer {
    if (!QueryOptimizer.instance) {
      QueryOptimizer.instance = new QueryOptimizer();
    }
    return QueryOptimizer.instance;
  }

  /**
   * Optimize product queries with proper indexing and eager loading
   */
  async optimizeProductQuery(params: {
    where?: Prisma.ProductWhereInput;
    include?: Prisma.ProductInclude;
    orderBy?: Prisma.ProductOrderByWithRelationInput;
    take?: number;
    skip?: number;
  }) {
    const queryId = this.generateQueryId('product', params);
    const startTime = Date.now();

    try {
      // Optimized include strategy to prevent N+1 queries
      const optimizedInclude: Prisma.ProductInclude = {
        // Always include essential relations
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo: true,
          },
        },
        // Optimized images with main image priority
        images: {
          take: 10, // Limit to prevent large payloads
          orderBy: [
            { isMain: 'desc' },
            { sortOrder: 'asc' },
          ],
          include: {
            media: {
              select: {
                id: true,
                url: true,
                alt: true,
                title: true,
                width: true,
                height: true,
              },
            },
          },
        },
        // Optimized attributes with values
        attributes: {
          include: {
            attribute: {
              select: {
                id: true,
                name: true,
                type: true,
                slug: true,
              },
            },
            attributeValue: {
              select: {
                id: true,
                value: true,
                slug: true,
                color: true,
              },
            },
          },
        },
        // Count aggregations for performance
        _count: {
          select: {
            reviews: true,
            cartItems: true,
            wishlistItems: true,
          },
        },
        ...params.include,
      };

      // Optimize WHERE clause for better index usage
      const optimizedWhere = this.optimizeWhereClause(params.where);

      // Optimize ORDER BY for index utilization
      const optimizedOrderBy = this.optimizeOrderBy(params.orderBy);

      const result = await prisma.product.findMany({
        where: optimizedWhere,
        include: optimizedInclude,
        orderBy: optimizedOrderBy,
        take: params.take,
        skip: params.skip,
      });

      this.recordQueryMetrics(queryId, Date.now() - startTime, result.length);
      return result;
    } catch (error) {
      this.recordQueryMetrics(queryId, Date.now() - startTime, 0, error);
      throw error;
    }
  }

  /**
   * Optimize category tree queries with recursive CTE simulation
   */
  async optimizeCategoryTreeQuery(rootId?: string) {
    const queryId = this.generateQueryId('categoryTree', { rootId });
    const startTime = Date.now();

    try {
      // Use a single query with nested includes instead of multiple queries
      const result = await prisma.category.findMany({
        where: {
          parentId: rootId || null,
          isActive: true,
        },
        include: {
          children: {
            where: { isActive: true },
            orderBy: { sortOrder: 'asc' },
            include: {
              children: {
                where: { isActive: true },
                orderBy: { sortOrder: 'asc' },
                include: {
                  _count: {
                    select: {
                      products: {
                        where: { status: 'ACTIVE' },
                      },
                    },
                  },
                },
              },
              _count: {
                select: {
                  products: {
                    where: { status: 'ACTIVE' },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              products: {
                where: { status: 'ACTIVE' },
              },
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      });

      this.recordQueryMetrics(queryId, Date.now() - startTime, result.length);
      return result;
    } catch (error) {
      this.recordQueryMetrics(queryId, Date.now() - startTime, 0, error);
      throw error;
    }
  }

  /**
   * Optimize search queries with full-text search simulation
   */
  async optimizeSearchQuery(params: {
    query: string;
    filters?: {
      categoryId?: string;
      brandId?: string;
      minPrice?: number;
      maxPrice?: number;
      tags?: string[];
    };
    pagination?: {
      page: number;
      limit: number;
    };
  }) {
    const queryId = this.generateQueryId('search', params);
    const startTime = Date.now();

    try {
      // Build optimized search where clause
      const searchTerms = params.query.split(' ').filter(term => term.length > 2);
      
      const where: Prisma.ProductWhereInput = {
        AND: [
          // Text search optimization
          {
            OR: [
              // Primary name search (highest weight)
              {
                name: {
                  contains: params.query,
                  mode: 'insensitive',
                },
              },
              // SKU exact match (high priority)
              {
                sku: {
                  contains: params.query,
                  mode: 'insensitive',
                },
              },
              // Description search (lower weight)
              {
                description: {
                  contains: params.query,
                  mode: 'insensitive',
                },
              },
              // Individual term search
              ...searchTerms.map(term => ({
                OR: [
                  { name: { contains: term, mode: 'insensitive' as const } },
                  { description: { contains: term, mode: 'insensitive' as const } },
                ],
              })),
            ],
          },
          // Status filter (for index optimization)
          { status: 'ACTIVE' },
          // Filters
          ...(params.filters?.categoryId ? [{ categoryId: params.filters.categoryId }] : []),
          ...(params.filters?.brandId ? [{ brandId: params.filters.brandId }] : []),
          ...(params.filters?.minPrice ? [{ price: { gte: params.filters.minPrice } }] : []),
          ...(params.filters?.maxPrice ? [{ price: { lte: params.filters.maxPrice } }] : []),
          ...(params.filters?.tags?.length ? [{ tags: { hasSome: params.filters.tags } }] : []),
        ],
      };

      // Optimized order by for search relevance
      const orderBy: Prisma.ProductOrderByWithRelationInput[] = [
        // Exact name match first
        { name: 'asc' },
        // Then by popularity (review count)
        { reviews: { _count: 'desc' } },
        // Finally by date
        { createdAt: 'desc' },
      ];

      const { page = 1, limit = 20 } = params.pagination || {};
      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        this.optimizeProductQuery({
          where,
          orderBy: orderBy[0], // Use first order criteria
          take: limit,
          skip,
        }),
        prisma.product.count({ where }),
      ]);

      const result = {
        data: products,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1,
        },
      };

      this.recordQueryMetrics(queryId, Date.now() - startTime, products.length);
      return result;
    } catch (error) {
      this.recordQueryMetrics(queryId, Date.now() - startTime, 0, error);
      throw error;
    }
  }

  /**
   * Optimize aggregation queries for dashboard stats
   */
  async optimizeStatsQuery() {
    const queryId = this.generateQueryId('stats', {});
    const startTime = Date.now();

    try {
      // Use parallel execution for independent aggregations
      const [
        productStats,
        orderStats,
        userStats,
        recentOrders,
      ] = await Promise.all([
        // Product statistics
        prisma.product.groupBy({
          by: ['status'],
          _count: {
            _all: true,
          },
        }),
        
        // Order statistics for current month
        prisma.order.groupBy({
          by: ['status'],
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
          _count: {
            _all: true,
          },
          _sum: {
            totalAmount: true,
          },
        }),
        
        // User statistics
        prisma.user.aggregate({
          _count: {
            _all: true,
          },
          where: {
            isActive: true,
          },
        }),
        
        // Recent orders with minimal data
        prisma.order.findMany({
          take: 10,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            orderNumber: true,
            status: true,
            totalAmount: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        }),
      ]);

      const result = {
        products: this.processProductStats(productStats),
        orders: this.processOrderStats(orderStats),
        users: userStats,
        recentOrders,
      };

      this.recordQueryMetrics(queryId, Date.now() - startTime, 1);
      return result;
    } catch (error) {
      this.recordQueryMetrics(queryId, Date.now() - startTime, 0, error);
      throw error;
    }
  }

  /**
   * Optimize WHERE clause for better index usage
   */
  private optimizeWhereClause(where?: Prisma.ProductWhereInput): Prisma.ProductWhereInput {
    if (!where) return {};

    // Reorder conditions for optimal index usage
    const optimized: Prisma.ProductWhereInput = {};

    // 1. Equality conditions first (best for indexes)
    if (where.id) optimized.id = where.id;
    if (where.status) optimized.status = where.status;
    if (where.categoryId) optimized.categoryId = where.categoryId;
    if (where.brandId) optimized.brandId = where.brandId;
    if (where.featured !== undefined) optimized.featured = where.featured;

    // 2. Range conditions
    if (where.price) optimized.price = where.price;
    if (where.stock) optimized.stock = where.stock;
    if (where.createdAt) optimized.createdAt = where.createdAt;

    // 3. Array and text searches last
    if (where.tags) optimized.tags = where.tags;
    if (where.name) optimized.name = where.name;
    if (where.description) optimized.description = where.description;

    // 4. Complex conditions
    if (where.AND) optimized.AND = where.AND;
    if (where.OR) optimized.OR = where.OR;
    if (where.NOT) optimized.NOT = where.NOT;

    return optimized;
  }

  /**
   * Optimize ORDER BY for index utilization
   */
  private optimizeOrderBy(orderBy?: Prisma.ProductOrderByWithRelationInput): Prisma.ProductOrderByWithRelationInput {
    if (!orderBy) {
      // Default optimized order
      return { createdAt: 'desc' };
    }

    // Ensure we're using indexed fields for ordering
    const indexedFields = ['id', 'createdAt', 'updatedAt', 'name', 'price', 'status', 'featured'];
    
    // If ordering by non-indexed field, add secondary indexed field
    const primaryField = Object.keys(orderBy)[0];
    if (!indexedFields.includes(primaryField)) {
      return {
        ...orderBy,
        createdAt: 'desc', // Add secondary sort
      };
    }

    return orderBy;
  }

  /**
   * Generate query ID for caching and metrics
   */
  private generateQueryId(type: string, params: any): string {
    const hash = JSON.stringify(params);
    return `${type}_${hash.length}_${Date.now()}`;
  }

  /**
   * Record query performance metrics
   */
  private recordQueryMetrics(queryId: string, duration: number, resultCount: number, error?: any) {
    this.performanceMetrics.set(queryId, {
      duration,
      resultCount,
      timestamp: Date.now(),
      success: !error,
      error: error?.message,
    });

    // Keep only last 1000 metrics
    if (this.performanceMetrics.size > 1000) {
      const oldestKey = this.performanceMetrics.keys().next().value;
      this.performanceMetrics.delete(oldestKey);
    }

    // Log slow queries
    if (duration > 1000) {
      console.warn(`🐌 Slow query detected: ${queryId} took ${duration}ms`);
    }
  }

  /**
   * Process product statistics
   */
  private processProductStats(stats: any[]) {
    const result = {
      total: 0,
      active: 0,
      inactive: 0,
      outOfStock: 0,
    };

    stats.forEach(stat => {
      result.total += stat._count._all;
      switch (stat.status) {
        case 'ACTIVE':
          result.active = stat._count._all;
          break;
        case 'INACTIVE':
          result.inactive = stat._count._all;
          break;
        case 'OUT_OF_STOCK':
          result.outOfStock = stat._count._all;
          break;
      }
    });

    return result;
  }

  /**
   * Process order statistics
   */
  private processOrderStats(stats: any[]) {
    const result = {
      total: 0,
      pending: 0,
      completed: 0,
      cancelled: 0,
      revenue: 0,
    };

    stats.forEach(stat => {
      result.total += stat._count._all;
      result.revenue += stat._sum.totalAmount || 0;
      
      switch (stat.status) {
        case 'PENDING':
          result.pending = stat._count._all;
          break;
        case 'DELIVERED':
          result.completed = stat._count._all;
          break;
        case 'CANCELLED':
          result.cancelled = stat._count._all;
          break;
      }
    });

    return result;
  }

  /**
   * Get query performance analytics
   */
  getPerformanceAnalytics() {
    const metrics = Array.from(this.performanceMetrics.values());
    
    if (metrics.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        errorRate: 0,
      };
    }

    const totalQueries = metrics.length;
    const averageDuration = metrics.reduce((sum, m) => sum + m.duration, 0) / totalQueries;
    const slowQueries = metrics.filter(m => m.duration > 1000).length;
    const errors = metrics.filter(m => !m.success).length;
    const errorRate = (errors / totalQueries) * 100;

    return {
      totalQueries,
      averageDuration: Math.round(averageDuration),
      slowQueries,
      errorRate: Math.round(errorRate * 100) / 100,
      recentQueries: metrics.slice(-10).map(m => ({
        duration: m.duration,
        success: m.success,
        timestamp: m.timestamp,
      })),
    };
  }

  /**
   * Clear performance metrics
   */
  clearMetrics() {
    this.performanceMetrics.clear();
  }
}

interface QueryMetrics {
  duration: number;
  resultCount: number;
  timestamp: number;
  success: boolean;
  error?: string;
}

// Export singleton instance
export const queryOptimizer = QueryOptimizer.getInstance();