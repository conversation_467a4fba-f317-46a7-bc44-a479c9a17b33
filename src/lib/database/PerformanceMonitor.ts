/**
 * Database performance monitoring and optimization recommendations
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private queryMetrics = new Map<string, QueryPerformanceMetric>();
  private slowQueryThreshold = 1000; // 1 second
  private recommendations = new Set<PerformanceRecommendation>();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Record query execution metrics
   */
  recordQuery(
    queryType: string,
    duration: number,
    resultCount: number,
    params?: any
  ): void {
    const queryId = this.generateQueryId(queryType, params);
    
    const existing = this.queryMetrics.get(queryId);
    if (existing) {
      // Update existing metrics
      existing.executionCount++;
      existing.totalDuration += duration;
      existing.averageDuration = existing.totalDuration / existing.executionCount;
      existing.maxDuration = Math.max(existing.maxDuration, duration);
      existing.minDuration = Math.min(existing.minDuration, duration);
      existing.lastExecuted = Date.now();
      
      if (resultCount > existing.maxResultCount) {
        existing.maxResultCount = resultCount;
      }
    } else {
      // Create new metrics
      this.queryMetrics.set(queryId, {
        queryType,
        queryId,
        executionCount: 1,
        totalDuration: duration,
        averageDuration: duration,
        maxDuration: duration,
        minDuration: duration,
        maxResultCount: resultCount,
        lastExecuted: Date.now(),
        params: this.sanitizeParams(params),
      });
    }

    // Check for performance issues
    this.analyzeQueryPerformance(queryId, duration, resultCount);
  }

  /**
   * Analyze query performance and generate recommendations
   */
  private analyzeQueryPerformance(
    queryId: string,
    duration: number,
    resultCount: number
  ): void {
    const metric = this.queryMetrics.get(queryId);
    if (!metric) return;

    // Slow query detection
    if (duration > this.slowQueryThreshold) {
      this.addRecommendation({
        type: 'SLOW_QUERY',
        severity: 'high',
        queryId,
        message: `Query "${metric.queryType}" took ${duration}ms to execute`,
        suggestion: 'Consider adding indexes or optimizing the query structure',
        impact: 'User experience degradation',
      });
    }

    // Large result set detection
    if (resultCount > 1000) {
      this.addRecommendation({
        type: 'LARGE_RESULT_SET',
        severity: 'medium',
        queryId,
        message: `Query returned ${resultCount} results`,
        suggestion: 'Implement pagination or add more specific filters',
        impact: 'Memory usage and response time',
      });
    }

    // Frequent execution detection
    if (metric.executionCount > 100 && metric.averageDuration > 100) {
      this.addRecommendation({
        type: 'FREQUENT_SLOW_QUERY',
        severity: 'high',
        queryId,
        message: `Query executed ${metric.executionCount} times with average duration ${metric.averageDuration.toFixed(2)}ms`,
        suggestion: 'This query is a good candidate for caching or optimization',
        impact: 'Overall system performance',
      });
    }

    // N+1 query pattern detection
    if (metric.queryType.includes('findMany') && metric.executionCount > 10) {
      const recentExecutions = this.getRecentExecutions(queryId, 60000); // Last minute
      if (recentExecutions.length > 5) {
        this.addRecommendation({
          type: 'POSSIBLE_N_PLUS_ONE',
          severity: 'high',
          queryId,
          message: `Possible N+1 query pattern detected: ${recentExecutions.length} executions in the last minute`,
          suggestion: 'Use include/select to fetch related data in a single query or implement data loading batching',
          impact: 'Database connection exhaustion and poor performance',
        });
      }
    }
  }

  /**
   * Add performance recommendation
   */
  private addRecommendation(recommendation: PerformanceRecommendation): void {
    // Prevent duplicate recommendations
    const existingRec = Array.from(this.recommendations).find(
      rec => rec.type === recommendation.type && rec.queryId === recommendation.queryId
    );

    if (!existingRec) {
      this.recommendations.add({
        ...recommendation,
        timestamp: Date.now(),
      });

      // Log high severity recommendations
      if (recommendation.severity === 'high') {
        console.warn(`🚨 Performance Issue: ${recommendation.message}`);
        console.warn(`💡 Suggestion: ${recommendation.suggestion}`);
      }
    }
  }

  /**
   * Get recent executions for a query
   */
  private getRecentExecutions(queryId: string, timeWindowMs: number): number[] {
    // In a real implementation, this would track individual execution timestamps
    // For now, we'll simulate based on average frequency
    const metric = this.queryMetrics.get(queryId);
    if (!metric) return [];

    const timeSinceFirst = Date.now() - (metric.lastExecuted - (metric.executionCount * metric.averageDuration));
    const executionsInWindow = Math.round((timeWindowMs / timeSinceFirst) * metric.executionCount);
    
    return new Array(Math.min(executionsInWindow, metric.executionCount)).fill(0);
  }

  /**
   * Generate unique query ID for tracking
   */
  private generateQueryId(queryType: string, params?: any): string {
    if (!params) return queryType;
    
    // Create a normalized hash of the query structure
    const paramKeys = Object.keys(params || {}).sort();
    const structure = paramKeys.map(key => {
      const value = params[key];
      if (typeof value === 'object' && value !== null) {
        return `${key}:object`;
      }
      return `${key}:${typeof value}`;
    }).join(',');
    
    return `${queryType}:${structure}`;
  }

  /**
   * Sanitize parameters for storage (remove sensitive data)
   */
  private sanitizeParams(params?: any): any {
    if (!params) return {};
    
    const sanitized = { ...params };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];
    
    Object.keys(sanitized).forEach(key => {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = '[REDACTED]';
      }
    });
    
    return sanitized;
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): PerformanceSummary {
    const metrics = Array.from(this.queryMetrics.values());
    const recommendations = Array.from(this.recommendations);
    
    if (metrics.length === 0) {
      return {
        totalQueries: 0,
        averageQueryTime: 0,
        slowQueries: 0,
        recommendations: [],
        topSlowQueries: [],
        mostFrequentQueries: [],
      };
    }

    const totalExecutions = metrics.reduce((sum, m) => sum + m.executionCount, 0);
    const totalDuration = metrics.reduce((sum, m) => sum + m.totalDuration, 0);
    const slowQueries = metrics.filter(m => m.averageDuration > this.slowQueryThreshold).length;
    
    // Top 5 slowest queries
    const topSlowQueries = metrics
      .sort((a, b) => b.averageDuration - a.averageDuration)
      .slice(0, 5)
      .map(m => ({
        queryType: m.queryType,
        averageDuration: Math.round(m.averageDuration),
        executionCount: m.executionCount,
        maxDuration: m.maxDuration,
      }));

    // Top 5 most frequent queries
    const mostFrequentQueries = metrics
      .sort((a, b) => b.executionCount - a.executionCount)
      .slice(0, 5)
      .map(m => ({
        queryType: m.queryType,
        executionCount: m.executionCount,
        averageDuration: Math.round(m.averageDuration),
        totalDuration: Math.round(m.totalDuration),
      }));

    return {
      totalQueries: totalExecutions,
      averageQueryTime: Math.round(totalDuration / totalExecutions),
      slowQueries,
      recommendations: recommendations.slice(0, 10), // Top 10 recommendations
      topSlowQueries,
      mostFrequentQueries,
    };
  }

  /**
   * Get detailed metrics for a specific query
   */
  getQueryMetrics(queryId: string): QueryPerformanceMetric | null {
    return this.queryMetrics.get(queryId) || null;
  }

  /**
   * Get all performance recommendations
   */
  getRecommendations(severity?: 'low' | 'medium' | 'high'): PerformanceRecommendation[] {
    const recommendations = Array.from(this.recommendations);
    
    if (severity) {
      return recommendations.filter(rec => rec.severity === severity);
    }
    
    return recommendations.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  /**
   * Clear old metrics and recommendations
   */
  cleanup(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge;
    
    // Clear old metrics
    const metricsToDelete: string[] = [];
    this.queryMetrics.forEach((metric, key) => {
      if (metric.lastExecuted < cutoff) {
        metricsToDelete.push(key);
      }
    });
    
    metricsToDelete.forEach(key => {
      this.queryMetrics.delete(key);
    });

    // Clear old recommendations
    const recommendationsToDelete: PerformanceRecommendation[] = [];
    this.recommendations.forEach(rec => {
      if (rec.timestamp && rec.timestamp < cutoff) {
        recommendationsToDelete.push(rec);
      }
    });
    
    recommendationsToDelete.forEach(rec => {
      this.recommendations.delete(rec);
    });

    console.log(`🧹 Performance monitor cleanup: Removed ${metricsToDelete.length} metrics and ${recommendationsToDelete.length} recommendations`);
  }

  /**
   * Generate performance report
   */
  generateReport(): PerformanceReport {
    const summary = this.getPerformanceSummary();
    const recommendations = this.getRecommendations();
    
    return {
      generated: new Date().toISOString(),
      summary,
      recommendations,
      metrics: {
        totalTrackedQueries: this.queryMetrics.size,
        activeRecommendations: this.recommendations.size,
      },
      insights: this.generateInsights(summary),
    };
  }

  /**
   * Generate performance insights
   */
  private generateInsights(summary: PerformanceSummary): string[] {
    const insights: string[] = [];
    
    if (summary.averageQueryTime > 500) {
      insights.push('Overall query performance is slower than recommended (>500ms average)');
    }
    
    if (summary.slowQueries > summary.totalQueries * 0.1) {
      insights.push('More than 10% of queries are classified as slow - consider optimization');
    }
    
    const nPlusOneRecs = summary.recommendations.filter(r => r.type === 'POSSIBLE_N_PLUS_ONE');
    if (nPlusOneRecs.length > 0) {
      insights.push(`Detected ${nPlusOneRecs.length} possible N+1 query patterns - high priority for optimization`);
    }
    
    if (summary.mostFrequentQueries.length > 0) {
      const topQuery = summary.mostFrequentQueries[0];
      insights.push(`Most frequent query: "${topQuery.queryType}" executed ${topQuery.executionCount} times`);
    }
    
    if (insights.length === 0) {
      insights.push('Query performance appears to be within acceptable ranges');
    }
    
    return insights;
  }

  /**
   * Set slow query threshold
   */
  setSlowQueryThreshold(milliseconds: number): void {
    this.slowQueryThreshold = milliseconds;
  }

  /**
   * Reset all metrics and recommendations
   */
  reset(): void {
    this.queryMetrics.clear();
    this.recommendations.clear();
    console.log('🔄 Performance monitor reset');
  }
}

// Type definitions
interface QueryPerformanceMetric {
  queryType: string;
  queryId: string;
  executionCount: number;
  totalDuration: number;
  averageDuration: number;
  maxDuration: number;
  minDuration: number;
  maxResultCount: number;
  lastExecuted: number;
  params?: any;
}

interface PerformanceRecommendation {
  type: 'SLOW_QUERY' | 'LARGE_RESULT_SET' | 'FREQUENT_SLOW_QUERY' | 'POSSIBLE_N_PLUS_ONE' | 'MISSING_INDEX';
  severity: 'low' | 'medium' | 'high';
  queryId: string;
  message: string;
  suggestion: string;
  impact: string;
  timestamp?: number;
}

interface PerformanceSummary {
  totalQueries: number;
  averageQueryTime: number;
  slowQueries: number;
  recommendations: PerformanceRecommendation[];
  topSlowQueries: Array<{
    queryType: string;
    averageDuration: number;
    executionCount: number;
    maxDuration: number;
  }>;
  mostFrequentQueries: Array<{
    queryType: string;
    executionCount: number;
    averageDuration: number;
    totalDuration: number;
  }>;
}

interface PerformanceReport {
  generated: string;
  summary: PerformanceSummary;
  recommendations: PerformanceRecommendation[];
  metrics: {
    totalTrackedQueries: number;
    activeRecommendations: number;
  };
  insights: string[];
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();