import { Prisma } from '@prisma/client';

/**
 * Advanced query builder with N+1 prevention and optimization strategies
 */
export class QueryBuilder {
  private static instance: QueryBuilder;
  private queryCache = new Map<string, any>();
  private includeStrategies = new Map<string, IncludeStrategy>();

  static getInstance(): QueryBuilder {
    if (!QueryBuilder.instance) {
      QueryBuilder.instance = new QueryBuilder();
    }
    return QueryBuilder.instance;
  }

  constructor() {
    this.initializeIncludeStrategies();
  }

  /**
   * Initialize optimized include strategies for different entities
   */
  private initializeIncludeStrategies(): void {
    // Product include strategy
    this.includeStrategies.set('product', {
      relations: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
            parentId: true,
          },
        },
        brand: {
          select: {
            id: true,
            name: true,
            slug: true,
            logo: true,
          },
        },
        images: {
          take: 10,
          orderBy: [
            { isMain: 'desc' },
            { sortOrder: 'asc' },
          ],
          include: {
            media: {
              select: {
                id: true,
                url: true,
                alt: true,
                title: true,
                width: true,
                height: true,
              },
            },
          },
        },
        attributes: {
          include: {
            attribute: {
              select: {
                id: true,
                name: true,
                type: true,
                slug: true,
              },
            },
            attributeValue: {
              select: {
                id: true,
                value: true,
                slug: true,
                color: true,
              },
            },
          },
        },
        _count: {
          select: {
            reviews: true,
            cartItems: true,
            wishlistItems: true,
          },
        },
      },
      aggregations: {
        avgRating: true,
        reviewCount: true,
      },
    });

    // Category include strategy
    this.includeStrategies.set('category', {
      relations: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
          select: {
            id: true,
            name: true,
            slug: true,
            sortOrder: true,
          },
        },
        _count: {
          select: {
            products: {
              where: { status: 'ACTIVE' },
            },
            children: {
              where: { isActive: true },
            },
          },
        },
      },
    });

    // Order include strategy
    this.includeStrategies.set('order', {
      relations: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                sku: true,
                images: {
                  take: 1,
                  where: { isMain: true },
                  include: {
                    media: {
                      select: {
                        id: true,
                        url: true,
                        alt: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    // User include strategy
    this.includeStrategies.set('user', {
      relations: {
        addresses: {
          where: { isDefault: true },
          take: 1,
        },
        _count: {
          select: {
            orders: true,
            reviews: true,
            wishlistItems: true,
          },
        },
      },
    });
  }

  /**
   * Build optimized query with N+1 prevention
   */
  buildOptimizedQuery<T>(
    entity: string,
    params: QueryParams
  ): OptimizedQuery<T> {
    const strategy = this.includeStrategies.get(entity);
    
    const query: OptimizedQuery<T> = {
      where: this.optimizeWhereClause(params.where),
      include: this.buildOptimizedInclude(strategy?.relations, params.include),
      orderBy: this.optimizeOrderBy(params.orderBy),
      take: params.take,
      skip: params.skip,
    };

    // Add cursor-based pagination for large datasets
    if (params.cursor) {
      query.cursor = { id: params.cursor };
      delete query.skip;
    }

    return query;
  }

  /**
   * Build optimized include with selective field loading
   */
  private buildOptimizedInclude(
    strategyIncludes?: Record<string, any>,
    customIncludes?: Record<string, any>
  ): Record<string, any> | undefined {
    if (!strategyIncludes && !customIncludes) {
      return undefined;
    }

    // Merge strategy includes with custom includes
    const include = { ...strategyIncludes };
    
    if (customIncludes) {
      Object.keys(customIncludes).forEach(key => {
        if (include[key] && typeof include[key] === 'object' && typeof customIncludes[key] === 'object') {
          // Merge nested includes
          include[key] = {
            ...include[key],
            ...customIncludes[key],
          };
        } else {
          include[key] = customIncludes[key];
        }
      });
    }

    return include;
  }

  /**
   * Optimize WHERE clause for better index usage
   */
  private optimizeWhereClause(where?: any): any {
    if (!where) return undefined;

    // Reorder conditions for optimal index usage
    const optimized: any = {};

    // 1. Equality conditions first (best for indexes)
    const equalityFields = ['id', 'status', 'categoryId', 'brandId', 'userId', 'featured'];
    equalityFields.forEach(field => {
      if (where[field] !== undefined) {
        optimized[field] = where[field];
      }
    });

    // 2. Range conditions
    const rangeFields = ['price', 'stock', 'createdAt', 'updatedAt'];
    rangeFields.forEach(field => {
      if (where[field] !== undefined) {
        optimized[field] = where[field];
      }
    });

    // 3. Array and text searches
    const searchFields = ['tags', 'name', 'description', 'email'];
    searchFields.forEach(field => {
      if (where[field] !== undefined) {
        optimized[field] = where[field];
      }
    });

    // 4. Complex conditions
    if (where.AND) optimized.AND = where.AND;
    if (where.OR) optimized.OR = where.OR;
    if (where.NOT) optimized.NOT = where.NOT;

    // Add remaining fields
    Object.keys(where).forEach(key => {
      if (!Object.prototype.hasOwnProperty.call(optimized, key)) {
        optimized[key] = where[key];
      }
    });

    return optimized;
  }

  /**
   * Optimize ORDER BY for index utilization
   */
  private optimizeOrderBy(orderBy?: any): any {
    if (!orderBy) {
      return { createdAt: 'desc' };
    }

    // Ensure we're using indexed fields for ordering
    const indexedFields = [
      'id', 'createdAt', 'updatedAt', 'name', 'price', 
      'status', 'featured', 'sortOrder', 'email'
    ];
    
    if (Array.isArray(orderBy)) {
      return orderBy;
    }

    const primaryField = Object.keys(orderBy)[0];
    
    // If ordering by non-indexed field, add secondary indexed field
    if (!indexedFields.includes(primaryField)) {
      return [orderBy, { createdAt: 'desc' }];
    }

    return orderBy;
  }

  /**
   * Build batch query to prevent N+1 problems
   */
  buildBatchQuery<T>(
    entity: string,
    ids: string[],
    relations?: string[]
  ): BatchQuery<T> {
    const strategy = this.includeStrategies.get(entity);
    
    // Optimize for batch loading
    const include: any = {};
    
    if (relations && strategy?.relations) {
      relations.forEach(relation => {
        if (strategy.relations[relation]) {
          include[relation] = strategy.relations[relation];
        }
      });
    } else if (strategy?.relations) {
      // Use all strategy relations for batch loading
      Object.assign(include, strategy.relations);
    }

    return {
      where: { id: { in: ids } },
      include,
      orderBy: { createdAt: 'desc' },
    };
  }

  /**
   * Create data loader for preventing N+1 queries
   */
  createDataLoader<T>(
    entity: string,
    keyField: string = 'id'
  ): DataLoader<T> {
    const batchLoadFn = async (keys: readonly string[]): Promise<T[]> => {
      const batchQuery = this.buildBatchQuery<T>(entity, [...keys]);
      
      // Execute batch query (this would use the actual Prisma model)
      // const results = await prisma[entity].findMany(batchQuery);
      
      // For now, return empty array
      const results: T[] = [];
      
      // Map results back to original key order
      const resultMap = new Map<string, T>();
      results.forEach((result: any) => {
        resultMap.set(result[keyField], result);
      });
      
      return keys.map(key => resultMap.get(key)!).filter(Boolean);
    };

    return new DataLoader(batchLoadFn, {
      maxBatchSize: 100,
      cacheKeyFn: (key) => key,
    });
  }

  /**
   * Optimize aggregation queries
   */
  buildAggregationQuery(
    entity: string,
    aggregations: AggregationParams
  ): AggregationQuery {
    const query: AggregationQuery = {
      where: aggregations.where,
    };

    // Build _count aggregation
    if (aggregations.count) {
      query._count = aggregations.count === true 
        ? { _all: true }
        : aggregations.count;
    }

    // Build _sum aggregation
    if (aggregations.sum) {
      query._sum = aggregations.sum;
    }

    // Build _avg aggregation
    if (aggregations.avg) {
      query._avg = aggregations.avg;
    }

    // Build _min aggregation
    if (aggregations.min) {
      query._min = aggregations.min;
    }

    // Build _max aggregation
    if (aggregations.max) {
      query._max = aggregations.max;
    }

    // Add GROUP BY if specified
    if (aggregations.groupBy) {
      query.by = aggregations.groupBy;
    }

    return query;
  }

  /**
   * Build full-text search query
   */
  buildSearchQuery(
    entity: string,
    searchTerm: string,
    fields: string[] = ['name', 'description']
  ): SearchQuery {
    const searchConditions = fields.map(field => ({
      [field]: {
        contains: searchTerm,
        mode: 'insensitive' as const,
      },
    }));

    // For single term, use OR across fields
    let whereClause: any = {
      OR: searchConditions,
    };

    // For multiple terms, create more complex search
    const terms = searchTerm.split(' ').filter(term => term.length > 2);
    if (terms.length > 1) {
      whereClause = {
        AND: terms.map(term => ({
          OR: fields.map(field => ({
            [field]: {
              contains: term,
              mode: 'insensitive' as const,
            },
          })),
        })),
      };
    }

    const strategy = this.includeStrategies.get(entity);

    return {
      where: whereClause,
      include: strategy?.relations,
      orderBy: [
        // Exact matches first
        ...fields.map(field => ({
          [field]: 'asc' as const,
        })),
        // Then by relevance (creation date)
        { createdAt: 'desc' as const },
      ],
    };
  }

  /**
   * Cache frequently used queries
   */
  async getCachedQuery<T>(
    cacheKey: string,
    queryFn: () => Promise<T>,
    ttl: number = 300000 // 5 minutes
  ): Promise<T> {
    const cached = this.queryCache.get(cacheKey);
    
    if (cached && cached.expiry > Date.now()) {
      return cached.data;
    }

    const data = await queryFn();
    
    this.queryCache.set(cacheKey, {
      data,
      expiry: Date.now() + ttl,
    });

    return data;
  }

  /**
   * Clear query cache
   */
  clearCache(pattern?: string): void {
    if (!pattern) {
      this.queryCache.clear();
      return;
    }

    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const keysToDelete: string[] = [];

    this.queryCache.forEach((_, key) => {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      this.queryCache.delete(key);
    });
  }

  /**
   * Get query performance metrics
   */
  getMetrics(): QueryMetrics {
    return {
      cacheSize: this.queryCache.size,
      strategiesCount: this.includeStrategies.size,
      activeQueries: this.queryCache.size,
    };
  }
}

// Simple DataLoader implementation
class DataLoader<T> {
  private cache = new Map<string, Promise<T>>();
  private batchScheduled = false;
  private currentBatch: Array<{
    key: string;
    resolve: (value: T) => void;
    reject: (error: any) => void;
  }> = [];

  constructor(
    private batchLoadFn: (keys: readonly string[]) => Promise<T[]>,
    private options: {
      maxBatchSize?: number;
      cacheKeyFn?: (key: string) => string;
    } = {}
  ) {}

  async load(key: string): Promise<T> {
    const cacheKey = this.options.cacheKeyFn?.(key) || key;
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Create promise for this key
    const promise = new Promise<T>((resolve, reject) => {
      this.currentBatch.push({ key, resolve, reject });
      
      if (!this.batchScheduled) {
        this.batchScheduled = true;
        process.nextTick(() => this.executeBatch());
      }
    });

    // Cache the promise
    this.cache.set(cacheKey, promise);
    
    return promise;
  }

  private async executeBatch(): Promise<void> {
    const batch = this.currentBatch;
    this.currentBatch = [];
    this.batchScheduled = false;

    try {
      const keys = batch.map(item => item.key);
      const results = await this.batchLoadFn(keys);
      
      batch.forEach((item, index) => {
        if (results[index]) {
          item.resolve(results[index]);
        } else {
          item.reject(new Error(`No result for key: ${item.key}`));
        }
      });
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
  }

  clear(key?: string): void {
    if (key) {
      const cacheKey = this.options.cacheKeyFn?.(key) || key;
      this.cache.delete(cacheKey);
    } else {
      this.cache.clear();
    }
  }
}

// Type definitions
interface QueryParams {
  where?: any;
  include?: any;
  orderBy?: any;
  take?: number;
  skip?: number;
  cursor?: string;
}

interface OptimizedQuery<T> {
  where?: any;
  include?: any;
  orderBy?: any;
  take?: number;
  skip?: number;
  cursor?: any;
}

interface BatchQuery<T> {
  where: any;
  include?: any;
  orderBy?: any;
}

interface SearchQuery {
  where: any;
  include?: any;
  orderBy: any;
}

interface AggregationQuery {
  where?: any;
  _count?: any;
  _sum?: any;
  _avg?: any;
  _min?: any;
  _max?: any;
  by?: string[];
}

interface AggregationParams {
  where?: any;
  count?: boolean | any;
  sum?: any;
  avg?: any;
  min?: any;
  max?: any;
  groupBy?: string[];
}

interface IncludeStrategy {
  relations: Record<string, any>;
  aggregations?: Record<string, boolean>;
}

interface QueryMetrics {
  cacheSize: number;
  strategiesCount: number;
  activeQueries: number;
}

// Export singleton instance
export const queryBuilder = QueryBuilder.getInstance();