/**
 * Database indexing strategy and recommendations for NS Shop
 * This file contains recommended indexes for optimal query performance
 */

export interface IndexRecommendation {
  table: string;
  columns: string[];
  type: 'btree' | 'gin' | 'gist' | 'hash' | 'unique' | 'partial';
  condition?: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  estimatedImprovement: string;
}

export class IndexingStrategy {
  private static instance: IndexingStrategy;

  static getInstance(): IndexingStrategy {
    if (!IndexingStrategy.instance) {
      IndexingStrategy.instance = new IndexingStrategy();
    }
    return IndexingStrategy.instance;
  }

  /**
   * Get all recommended indexes for the NS Shop database
   */
  getRecommendedIndexes(): IndexRecommendation[] {
    return [
      // Product table indexes
      {
        table: 'products',
        columns: ['status', 'featured', 'createdAt'],
        type: 'btree',
        priority: 'high',
        reason: 'Optimize product listing queries with status and featured filters',
        estimatedImprovement: '60-80% faster product listings',
      },
      {
        table: 'products',
        columns: ['categoryId', 'status'],
        type: 'btree',
        priority: 'high',
        reason: 'Fast category-based product filtering',
        estimatedImprovement: '50-70% faster category pages',
      },
      {
        table: 'products',
        columns: ['brandId', 'status'],
        type: 'btree',
        priority: 'high',
        reason: 'Fast brand-based product filtering',
        estimatedImprovement: '50-70% faster brand pages',
      },
      {
        table: 'products',
        columns: ['price'],
        type: 'btree',
        priority: 'medium',
        reason: 'Optimize price range filtering and sorting',
        estimatedImprovement: '40-60% faster price filters',
      },
      {
        table: 'products',
        columns: ['name'],
        type: 'gin',
        priority: 'high',
        reason: 'Full-text search optimization for product names',
        estimatedImprovement: '70-90% faster text search',
      },
      {
        table: 'products',
        columns: ['sku'],
        type: 'unique',
        priority: 'high',
        reason: 'Unique constraint and fast SKU lookups',
        estimatedImprovement: 'Instant SKU searches',
      },
      {
        table: 'products',
        columns: ['slug'],
        type: 'unique',
        priority: 'high',
        reason: 'Fast product page loads by slug',
        estimatedImprovement: 'Instant slug lookups',
      },
      {
        table: 'products',
        columns: ['tags'],
        type: 'gin',
        priority: 'medium',
        reason: 'Array operations for tag filtering',
        estimatedImprovement: '50-70% faster tag filters',
      },

      // Category table indexes
      {
        table: 'categories',
        columns: ['slug'],
        type: 'unique',
        priority: 'high',
        reason: 'Fast category page loads by slug',
        estimatedImprovement: 'Instant category lookups',
      },
      {
        table: 'categories',
        columns: ['parentId', 'isActive', 'sortOrder'],
        type: 'btree',
        priority: 'high',
        reason: 'Optimize category tree queries',
        estimatedImprovement: '60-80% faster category menus',
      },
      {
        table: 'categories',
        columns: ['isActive', 'sortOrder'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast active category listings',
        estimatedImprovement: '40-60% faster category lists',
      },

      // User table indexes
      {
        table: 'users',
        columns: ['email'],
        type: 'unique',
        priority: 'high',
        reason: 'Fast authentication and unique email constraint',
        estimatedImprovement: 'Instant login lookups',
      },
      {
        table: 'users',
        columns: ['isActive', 'createdAt'],
        type: 'btree',
        priority: 'medium',
        reason: 'Admin user management queries',
        estimatedImprovement: '40-60% faster user admin pages',
      },

      // Order table indexes
      {
        table: 'orders',
        columns: ['userId', 'createdAt'],
        type: 'btree',
        priority: 'high',
        reason: 'Fast user order history',
        estimatedImprovement: '60-80% faster order history',
      },
      {
        table: 'orders',
        columns: ['status', 'createdAt'],
        type: 'btree',
        priority: 'high',
        reason: 'Admin order management by status',
        estimatedImprovement: '50-70% faster order admin',
      },
      {
        table: 'orders',
        columns: ['orderNumber'],
        type: 'unique',
        priority: 'high',
        reason: 'Fast order lookups by order number',
        estimatedImprovement: 'Instant order tracking',
      },
      {
        table: 'orders',
        columns: ['paymentStatus'],
        type: 'btree',
        priority: 'medium',
        reason: 'Payment processing queries',
        estimatedImprovement: '40-60% faster payment reports',
      },

      // Cart table indexes
      {
        table: 'carts',
        columns: ['userId'],
        type: 'unique',
        priority: 'high',
        reason: 'One cart per user constraint and fast lookup',
        estimatedImprovement: 'Instant cart access',
      },

      // Cart items table indexes
      {
        table: 'cartItems',
        columns: ['cartId', 'productId'],
        type: 'unique',
        priority: 'high',
        reason: 'Prevent duplicate items and fast cart operations',
        estimatedImprovement: 'Instant cart item operations',
      },
      {
        table: 'cartItems',
        columns: ['productId'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast product-cart relationship queries',
        estimatedImprovement: '40-60% faster cart analytics',
      },

      // Product images table indexes
      {
        table: 'productImages',
        columns: ['productId', 'sortOrder'],
        type: 'btree',
        priority: 'high',
        reason: 'Fast image loading for products',
        estimatedImprovement: '50-70% faster product image loads',
      },
      {
        table: 'productImages',
        columns: ['isMain', 'productId'],
        type: 'partial',
        condition: 'isMain = true',
        priority: 'medium',
        reason: 'Fast main image lookups',
        estimatedImprovement: '60-80% faster main image queries',
      },

      // Product attributes table indexes
      {
        table: 'productAttributes',
        columns: ['productId', 'attributeId'],
        type: 'unique',
        priority: 'high',
        reason: 'Prevent duplicate attributes and fast filtering',
        estimatedImprovement: '50-70% faster attribute filters',
      },
      {
        table: 'productAttributes',
        columns: ['attributeId', 'attributeValueId'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast attribute-based product filtering',
        estimatedImprovement: '40-60% faster attribute searches',
      },

      // Attributes table indexes
      {
        table: 'attributes',
        columns: ['slug'],
        type: 'unique',
        priority: 'medium',
        reason: 'Fast attribute lookups by slug',
        estimatedImprovement: '50-70% faster attribute admin',
      },
      {
        table: 'attributes',
        columns: ['isFilterable', 'sortOrder'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast filterable attribute queries',
        estimatedImprovement: '40-60% faster filter generation',
      },

      // Attribute values table indexes
      {
        table: 'attributeValues',
        columns: ['attributeId', 'sortOrder'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast attribute value listings',
        estimatedImprovement: '40-60% faster attribute value queries',
      },

      // Reviews table indexes
      {
        table: 'reviews',
        columns: ['productId', 'isApproved', 'createdAt'],
        type: 'btree',
        priority: 'high',
        reason: 'Fast product review loading',
        estimatedImprovement: '60-80% faster review displays',
      },
      {
        table: 'reviews',
        columns: ['userId', 'createdAt'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast user review history',
        estimatedImprovement: '40-60% faster user reviews',
      },
      {
        table: 'reviews',
        columns: ['rating'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast rating-based filtering',
        estimatedImprovement: '40-60% faster rating filters',
      },

      // Media table indexes
      {
        table: 'media',
        columns: ['type', 'isActive'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast media type filtering',
        estimatedImprovement: '40-60% faster media admin',
      },
      {
        table: 'media',
        columns: ['filename'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast file lookups',
        estimatedImprovement: '40-60% faster file operations',
      },

      // Inventory table indexes
      {
        table: 'inventoryMovements',
        columns: ['productId', 'createdAt'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast inventory history queries',
        estimatedImprovement: '50-70% faster inventory reports',
      },
      {
        table: 'inventoryMovements',
        columns: ['type', 'createdAt'],
        type: 'btree',
        priority: 'low',
        reason: 'Inventory movement type analysis',
        estimatedImprovement: '30-50% faster inventory analytics',
      },

      // Notification table indexes
      {
        table: 'notifications',
        columns: ['userId', 'isRead', 'createdAt'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast user notification queries',
        estimatedImprovement: '50-70% faster notification loading',
      },

      // Audit logs table indexes
      {
        table: 'auditLogs',
        columns: ['entity', 'entityId', 'createdAt'],
        type: 'btree',
        priority: 'medium',
        reason: 'Fast audit trail queries',
        estimatedImprovement: '50-70% faster audit queries',
      },
      {
        table: 'auditLogs',
        columns: ['userId', 'createdAt'],
        type: 'btree',
        priority: 'low',
        reason: 'User activity tracking',
        estimatedImprovement: '30-50% faster user audit',
      },
    ];
  }

  /**
   * Get SQL statements to create the recommended indexes
   */
  generateIndexCreationSQL(): string[] {
    const indexes = this.getRecommendedIndexes();
    const sqlStatements: string[] = [];

    indexes.forEach((index, i) => {
      const indexName = `idx_${index.table}_${index.columns.join('_')}`;
      let sql = '';

      switch (index.type) {
        case 'unique':
          sql = `CREATE UNIQUE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" (${index.columns.map(col => `"${col}"`).join(', ')});`;
          break;
        
        case 'gin':
          sql = `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" USING GIN (${index.columns.map(col => `"${col}"`).join(', ')});`;
          break;
        
        case 'gist':
          sql = `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" USING GIST (${index.columns.map(col => `"${col}"`).join(', ')});`;
          break;
        
        case 'partial':
          sql = `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" (${index.columns.map(col => `"${col}"`).join(', ')}) WHERE ${index.condition};`;
          break;
        
        case 'btree':
        case 'hash':
        default:
          const method = index.type === 'hash' ? 'USING HASH' : '';
          sql = `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" ${method} (${index.columns.map(col => `"${col}"`).join(', ')});`;
          break;
      }

      sqlStatements.push(`-- ${index.reason}\n-- Expected improvement: ${index.estimatedImprovement}\n${sql}\n`);
    });

    return sqlStatements;
  }

  /**
   * Get indexes by priority
   */
  getIndexesByPriority(priority: 'high' | 'medium' | 'low'): IndexRecommendation[] {
    return this.getRecommendedIndexes().filter(index => index.priority === priority);
  }

  /**
   * Get indexes for a specific table
   */
  getIndexesForTable(tableName: string): IndexRecommendation[] {
    return this.getRecommendedIndexes().filter(index => index.table === tableName);
  }

  /**
   * Generate Prisma migration file content
   */
  generatePrismaMigration(): string {
    const indexes = this.getRecommendedIndexes();
    const highPriorityIndexes = indexes.filter(index => index.priority === 'high');
    
    let migration = `-- CreateIndex migration for NS Shop optimization
-- This migration creates high-priority indexes for better performance

`;

    highPriorityIndexes.forEach(index => {
      migration += `-- ${index.reason}\n`;
      migration += `-- Expected improvement: ${index.estimatedImprovement}\n`;
      
      const indexName = `idx_${index.table}_${index.columns.join('_')}`;
      
      switch (index.type) {
        case 'unique':
          migration += `CREATE UNIQUE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" (${index.columns.map(col => `"${col}"`).join(', ')});\n`;
          break;
        case 'gin':
          migration += `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" USING GIN (${index.columns.map(col => `"${col}"`).join(', ')});\n`;
          break;
        case 'partial':
          migration += `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" (${index.columns.map(col => `"${col}"`).join(', ')}) WHERE ${index.condition};\n`;
          break;
        default:
          migration += `CREATE INDEX IF NOT EXISTS "${indexName}" ON "${index.table}" (${index.columns.map(col => `"${col}"`).join(', ')});\n`;
      }
      
      migration += '\n';
    });

    return migration;
  }

  /**
   * Analyze query patterns and suggest additional indexes
   */
  analyzeQueryPatterns(queryLogs: QueryPattern[]): IndexRecommendation[] {
    const suggestions: IndexRecommendation[] = [];
    const columnUsage = new Map<string, number>();

    // Analyze query patterns
    queryLogs.forEach(query => {
      // Extract WHERE clause columns
      query.whereColumns?.forEach(col => {
        const key = `${query.table}.${col}`;
        columnUsage.set(key, (columnUsage.get(key) || 0) + query.frequency);
      });

      // Extract ORDER BY columns
      query.orderByColumns?.forEach(col => {
        const key = `${query.table}.${col}`;
        columnUsage.set(key, (columnUsage.get(key) || 0) + query.frequency * 0.5);
      });
    });

    // Generate suggestions for frequently used columns
    columnUsage.forEach((frequency, key) => {
      if (frequency > 100) { // High usage threshold
        const [table, column] = key.split('.');
        
        // Check if index already exists in recommendations
        const existingIndex = this.getRecommendedIndexes().find(
          index => index.table === table && index.columns.includes(column)
        );

        if (!existingIndex) {
          suggestions.push({
            table,
            columns: [column],
            type: 'btree',
            priority: frequency > 500 ? 'high' : 'medium',
            reason: `High usage pattern detected (${frequency} occurrences)`,
            estimatedImprovement: '40-60% faster queries',
          });
        }
      }
    });

    return suggestions;
  }

  /**
   * Get performance impact estimation
   */
  estimatePerformanceImpact(): {
    querySpeedImprovement: string;
    memoryUsage: string;
    storageOverhead: string;
    maintenanceCost: string;
  } {
    const indexes = this.getRecommendedIndexes();
    const highPriorityCount = indexes.filter(i => i.priority === 'high').length;
    
    return {
      querySpeedImprovement: `${50 + (highPriorityCount * 5)}% average improvement`,
      memoryUsage: `+${Math.round(indexes.length * 0.5)}MB estimated`,
      storageOverhead: `+${Math.round(indexes.length * 2)}% storage increase`,
      maintenanceCost: 'Minimal - automatic PostgreSQL maintenance',
    };
  }
}

interface QueryPattern {
  table: string;
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE';
  whereColumns?: string[];
  orderByColumns?: string[];
  frequency: number;
  avgDuration: number;
}

// Export singleton instance
export const indexingStrategy = IndexingStrategy.getInstance();