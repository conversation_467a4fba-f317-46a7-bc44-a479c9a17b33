/**
 * Performance testing and monitoring utilities
 */

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  memory?: {
    used: number;
    total: number;
  };
  metadata?: Record<string, any>;
}

/**
 * Performance monitor class for tracking metrics
 */
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics = new Map<string, PerformanceMetrics>();
  private observers = new Map<string, PerformanceObserver>();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Start measuring performance for a given operation
   */
  start(name: string, metadata?: Record<string, any>): void {
    const startTime = performance.now();
    
    this.metrics.set(name, {
      name,
      startTime,
      memory: this.getMemoryUsage(),
      metadata,
    });

    // Use Performance API if available
    if (typeof performance.mark === 'function') {
      performance.mark(`${name}-start`);
    }
  }

  /**
   * End measuring performance for a given operation
   */
  end(name: string): PerformanceMetrics | null {
    const endTime = performance.now();
    const metric = this.metrics.get(name);

    if (!metric) {
      console.warn(`No performance metric found for: ${name}`);
      return null;
    }

    metric.endTime = endTime;
    metric.duration = endTime - metric.startTime;

    // Use Performance API if available
    if (typeof performance.mark === 'function' && typeof performance.measure === 'function') {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
    }

    return metric;
  }

  /**
   * Get memory usage information
   */
  private getMemoryUsage(): { used: number; total: number } | undefined {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
      };
    }
    return undefined;
  }

  /**
   * Get all recorded metrics
   */
  getMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Get specific metric by name
   */
  getMetric(name: string): PerformanceMetrics | null {
    return this.metrics.get(name) || null;
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear();
    if (typeof performance.clearMarks === 'function') {
      performance.clearMarks();
      performance.clearMeasures();
    }
  }

  /**
   * Create a performance observer for specific entry types
   */
  observe(entryTypes: string[], callback: (entries: PerformanceEntry[]) => void): void {
    if (typeof PerformanceObserver === 'undefined') {
      return;
    }

    const observerId = entryTypes.join('-');
    
    if (this.observers.has(observerId)) {
      return;
    }

    const observer = new PerformanceObserver((list) => {
      callback(list.getEntries());
    });

    try {
      observer.observe({ entryTypes });
      this.observers.set(observerId, observer);
    } catch (error) {
      console.warn('Failed to create performance observer:', error);
    }
  }

  /**
   * Disconnect all observers
   */
  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }
}

/**
 * Higher-order function to measure component render performance
 */
export function withPerformanceTracking<T extends Record<string, any>>(
  WrappedComponent: any,
  componentName?: string
): any {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';
  
  const WithPerformanceTracking = (props: T) => {
    const monitor = PerformanceMonitor.getInstance();
    
    // Component lifecycle tracking would require React import
    return WrappedComponent(props);
  };

  WithPerformanceTracking.displayName = `withPerformanceTracking(${displayName})`;
  
  return WithPerformanceTracking;
}

/**
 * Hook for measuring function execution time
 */
export function usePerformanceTimer(name: string) {
  const monitor = PerformanceMonitor.getInstance();

  const start = () => {
    monitor.start(name);
  };

  const end = () => {
    return monitor.end(name);
  };

  const measure = <T,>(fn: () => T): T => {
    start();
    const result = fn();
    end();
    return result;
  };

  const measureAsync = async <T,>(fn: () => Promise<T>): Promise<T> => {
    start();
    const result = await fn();
    end();
    return result;
  };

  return { start, end, measure, measureAsync };
}

/**
 * Hook for monitoring component re-renders
 */
export function useRenderCount(name: string): number {
  // Simplified version without React hooks
  if (process.env.NODE_ENV === 'development') {
    console.log(`${name} render tracked`);
  }
  return 0;
}

/**
 * Hook for detecting and logging unnecessary re-renders
 */
export function useWhyDidYouUpdate(name: string, props: Record<string, any>): void {
  // Simplified version without React hooks
  if (process.env.NODE_ENV === 'development') {
    console.log(`[WhyDidYouUpdate] ${name}`, props);
  }
}

/**
 * Performance testing utilities
 */
export class PerformanceTester {
  private results: Array<{
    name: string;
    duration: number;
    memory?: number;
    iterations: number;
    timestamp: number;
  }> = [];

  /**
   * Run a performance test with multiple iterations
   */
  async runTest<T>(
    name: string,
    testFunction: () => T | Promise<T>,
    iterations: number = 100
  ): Promise<{
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    totalDuration: number;
    iterations: number;
    memoryUsage?: number;
  }> {
    const durations: number[] = [];
    let memoryBefore: number | undefined;
    let memoryAfter: number | undefined;

    // Get initial memory
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      memoryBefore = (window.performance as any).memory.usedJSHeapSize;
    }

    console.log(`🧪 Running performance test: ${name} (${iterations} iterations)`);

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      
      await testFunction();
      
      const endTime = performance.now();
      durations.push(endTime - startTime);
    }

    // Get final memory
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      memoryAfter = (window.performance as any).memory.usedJSHeapSize;
    }

    const totalDuration = durations.reduce((sum, duration) => sum + duration, 0);
    const averageDuration = totalDuration / iterations;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    const memoryUsage = memoryBefore && memoryAfter ? memoryAfter - memoryBefore : undefined;

    const result = {
      averageDuration,
      minDuration,
      maxDuration,
      totalDuration,
      iterations,
      memoryUsage,
    };

    this.results.push({
      name,
      duration: averageDuration,
      memory: memoryUsage,
      iterations,
      timestamp: Date.now(),
    });

    console.log(`✅ ${name} completed:`, result);

    return result;
  }

  /**
   * Compare two test functions
   */
  async compareTests<T>(
    test1: { name: string; fn: () => T | Promise<T> },
    test2: { name: string; fn: () => T | Promise<T> },
    iterations: number = 100
  ): Promise<{
    test1Result: any;
    test2Result: any;
    winner: string;
    improvement: number;
  }> {
    console.log(`🔄 Comparing ${test1.name} vs ${test2.name}`);

    const [result1, result2] = await Promise.all([
      this.runTest(test1.name, test1.fn, iterations),
      this.runTest(test2.name, test2.fn, iterations),
    ]);

    const winner = result1.averageDuration < result2.averageDuration ? test1.name : test2.name;
    const improvement = Math.abs(
      ((result1.averageDuration - result2.averageDuration) / Math.max(result1.averageDuration, result2.averageDuration)) * 100
    );

    const comparison = {
      test1Result: result1,
      test2Result: result2,
      winner,
      improvement,
    };

    console.log(`🏆 Winner: ${winner} (${improvement.toFixed(2)}% faster)`);

    return comparison;
  }

  /**
   * Get all test results
   */
  getResults() {
    return this.results;
  }

  /**
   * Clear all results
   */
  clear() {
    this.results = [];
  }

  /**
   * Export results as CSV
   */
  exportResults(): string {
    const headers = ['Name', 'Duration (ms)', 'Memory (bytes)', 'Iterations', 'Timestamp'];
    const rows = this.results.map(result => [
      result.name,
      result.duration.toFixed(3),
      result.memory?.toString() || 'N/A',
      result.iterations.toString(),
      new Date(result.timestamp).toISOString(),
    ]);

    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}

/**
 * Create global performance monitor instance
 */
export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * Create global performance tester instance
 */
export const performanceTester = new PerformanceTester();

/**
 * Decorator for measuring function performance
 */
export function measurePerformance(name?: string) {
  return function <T extends (...args: any[]) => any>(
    target: any,
    propertyKey: string,
    descriptor: TypedPropertyDescriptor<T>
  ): TypedPropertyDescriptor<T> {
    const originalMethod = descriptor.value!;
    const methodName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      const monitor = PerformanceMonitor.getInstance();
      monitor.start(methodName);
      
      try {
        const result = originalMethod.apply(this, args);
        
        if (result instanceof Promise) {
          return result.finally(() => {
            monitor.end(methodName);
          });
        } else {
          monitor.end(methodName);
          return result;
        }
      } catch (error) {
        monitor.end(methodName);
        throw error;
      }
    } as T;

    return descriptor;
  };
}

/**
 * Utility functions for performance analysis
 */
export const PerformanceUtils = {
  /**
   * Check if performance API is available
   */
  isSupported(): boolean {
    return typeof performance !== 'undefined' && 
           typeof performance.now === 'function';
  },

  /**
   * Get current timestamp with high precision
   */
  now(): number {
    return this.isSupported() ? performance.now() : Date.now();
  },

  /**
   * Format duration in human-readable format
   */
  formatDuration(ms: number): string {
    if (ms < 1) {
      return `${(ms * 1000).toFixed(2)}μs`;
    } else if (ms < 1000) {
      return `${ms.toFixed(2)}ms`;
    } else {
      return `${(ms / 1000).toFixed(2)}s`;
    }
  },

  /**
   * Format memory usage in human-readable format
   */
  formatMemory(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let value = bytes;
    let unitIndex = 0;

    while (value >= 1024 && unitIndex < units.length - 1) {
      value /= 1024;
      unitIndex++;
    }

    return `${value.toFixed(2)} ${units[unitIndex]}`;
  },

  /**
   * Calculate percentiles from duration array
   */
  calculatePercentiles(durations: number[]): {
    p50: number;
    p95: number;
    p99: number;
  } {
    const sorted = [...durations].sort((a, b) => a - b);
    const len = sorted.length;

    return {
      p50: sorted[Math.floor(len * 0.5)],
      p95: sorted[Math.floor(len * 0.95)],
      p99: sorted[Math.floor(len * 0.99)],
    };
  },
};

