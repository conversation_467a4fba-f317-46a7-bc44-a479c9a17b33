import { CacheService, CacheOptions } from '@/lib/services/base';

/**
 * Redis-compatible cache service that can be swapped with actual Redis
 * Currently uses in-memory storage but can be easily replaced with Redis
 */
export class RedisCacheService extends CacheService {
  private static instance: RedisCacheService;
  private redis: any = null; // Would be Redis client in production
  
  // Advanced cache configurations
  private readonly defaultTTL = 300; // 5 minutes
  private readonly maxMemoryPolicy = 'allkeys-lru';
  private readonly keyPrefix = 'nsshop:';

  static getInstance(options?: CacheOptions): RedisCacheService {
    if (!RedisCacheService.instance) {
      RedisCacheService.instance = new RedisCacheService(options);
    }
    return RedisCacheService.instance;
  }

  constructor(options: CacheOptions = {}) {
    super(options);
    this.initializeRedis();
  }

  private async initializeRedis() {
    try {
      // In production, initialize Redis client here
      // this.redis = new Redis(process.env.REDIS_URL);
      console.log('🔄 Redis cache service initialized (in-memory mode)');
    } catch (error) {
      console.error('❌ Failed to initialize Redis:', error);
      // Fallback to in-memory cache
    }
  }

  // Enhanced set with Redis-like features
  async setAdvanced(
    key: string, 
    value: any, 
    options: {
      ttl?: number;
      nx?: boolean; // Set only if not exists
      xx?: boolean; // Set only if exists
      tags?: string[]; // For tag-based invalidation
    } = {}
  ): Promise<boolean> {
    const prefixedKey = this.keyPrefix + key;
    
    try {
      if (this.redis) {
        // Redis implementation
        const serialized = JSON.stringify({
          data: value,
          tags: options.tags || [],
          createdAt: Date.now(),
        });

        let result;
        if (options.nx) {
          result = await this.redis.set(prefixedKey, serialized, 'EX', options.ttl || this.defaultTTL, 'NX');
        } else if (options.xx) {
          result = await this.redis.set(prefixedKey, serialized, 'EX', options.ttl || this.defaultTTL, 'XX');
        } else {
          result = await this.redis.setex(prefixedKey, options.ttl || this.defaultTTL, serialized);
        }

        // Store tags for invalidation
        if (options.tags && options.tags.length > 0) {
          for (const tag of options.tags) {
            await this.redis.sadd(`${this.keyPrefix}tag:${tag}`, prefixedKey);
            await this.redis.expire(`${this.keyPrefix}tag:${tag}`, (options.ttl || this.defaultTTL) + 3600);
          }
        }

        return result === 'OK';
      } else {
        // Fallback to in-memory cache
        return super.set(key, value, options.ttl);
      }
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  // Enhanced get with metadata
  async getAdvanced(key: string): Promise<{
    data: any;
    metadata: {
      tags: string[];
      createdAt: number;
      ttl: number;
    };
  } | null> {
    const prefixedKey = this.keyPrefix + key;
    
    try {
      if (this.redis) {
        const result = await this.redis.get(prefixedKey);
        if (!result) return null;

        const parsed = JSON.parse(result);
        const ttl = await this.redis.ttl(prefixedKey);
        
        return {
          data: parsed.data,
          metadata: {
            tags: parsed.tags || [],
            createdAt: parsed.createdAt || Date.now(),
            ttl: ttl,
          },
        };
      } else {
        // Fallback to in-memory cache
        const data = super.get(key);
        return data ? {
          data,
          metadata: {
            tags: [],
            createdAt: Date.now(),
            ttl: -1,
          },
        } : null;
      }
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  // Tag-based invalidation
  async invalidateByTag(tag: string): Promise<number> {
    const tagKey = `${this.keyPrefix}tag:${tag}`;
    
    try {
      if (this.redis) {
        const keys = await this.redis.smembers(tagKey);
        if (keys.length === 0) return 0;

        // Delete all keys with this tag
        const pipeline = this.redis.pipeline();
        keys.forEach((key: string) => pipeline.del(key));
        pipeline.del(tagKey);
        
        const results = await pipeline.exec();
        return keys.length;
      } else {
        // Fallback implementation
        return await super.invalidatePattern(`*${tag}*`);
      }
    } catch (error) {
      console.error('Tag invalidation error:', error);
      return 0;
    }
  }

  // Bulk operations
  async msetAdvanced(
    items: Array<{
      key: string;
      value: any;
      ttl?: number;
      tags?: string[];
    }>
  ): Promise<boolean> {
    try {
      if (this.redis) {
        const pipeline = this.redis.pipeline();
        
        for (const item of items) {
          const prefixedKey = this.keyPrefix + item.key;
          const serialized = JSON.stringify({
            data: item.value,
            tags: item.tags || [],
            createdAt: Date.now(),
          });
          
          pipeline.setex(prefixedKey, item.ttl || this.defaultTTL, serialized);
          
          // Add tags
          if (item.tags && item.tags.length > 0) {
            for (const tag of item.tags) {
              pipeline.sadd(`${this.keyPrefix}tag:${tag}`, prefixedKey);
              pipeline.expire(`${this.keyPrefix}tag:${tag}`, (item.ttl || this.defaultTTL) + 3600);
            }
          }
        }
        
        await pipeline.exec();
        return true;
      } else {
        // Fallback to individual sets
        const keyValuePairs = items.map(item => ({
          key: item.key,
          val: item.value,
          ttl: item.ttl,
        }));
        return super.mset(keyValuePairs);
      }
    } catch (error) {
      console.error('Bulk set error:', error);
      return false;
    }
  }

  // Cache warming
  async warmCache(
    operations: Array<{
      key: string;
      factory: () => Promise<any>;
      ttl?: number;
      tags?: string[];
    }>
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    const promises = operations.map(async (op) => {
      try {
        const data = await op.factory();
        const result = await this.setAdvanced(op.key, data, {
          ttl: op.ttl,
          tags: op.tags,
        });
        
        if (result) success++;
        else failed++;
      } catch (error) {
        console.error(`Cache warming failed for key ${op.key}:`, error);
        failed++;
      }
    });

    await Promise.allSettled(promises);
    
    return { success, failed };
  }

  // Cache statistics with Redis INFO-like output
  async getDetailedStats(): Promise<{
    basic: any;
    redis?: {
      memory: {
        used: string;
        peak: string;
        fragmentation: number;
      };
      connections: number;
      operations: {
        total: number;
        per_second: number;
      };
      keyspace: {
        keys: number;
        expires: number;
        avg_ttl: number;
      };
    };
  }> {
    const basic = super.getStats();
    
    if (this.redis) {
      try {
        const info = await this.redis.info();
        const dbsize = await this.redis.dbsize();
        
        // Parse Redis INFO output
        const lines = info.split('\r\n');
        const stats: any = {};
        
        lines.forEach((line: string) => {
          if (line.includes(':')) {
            const [key, value] = line.split(':');
            stats[key] = isNaN(Number(value)) ? value : Number(value);
          }
        });

        return {
          basic,
          redis: {
            memory: {
              used: stats.used_memory_human || '0B',
              peak: stats.used_memory_peak_human || '0B',
              fragmentation: stats.mem_fragmentation_ratio || 1,
            },
            connections: stats.connected_clients || 0,
            operations: {
              total: stats.total_commands_processed || 0,
              per_second: stats.instantaneous_ops_per_sec || 0,
            },
            keyspace: {
              keys: dbsize,
              expires: stats.db0?.split(',')[1]?.split('=')[1] || 0,
              avg_ttl: stats.db0?.split(',')[2]?.split('=')[1] || 0,
            },
          },
        };
      } catch (error) {
        console.error('Failed to get Redis stats:', error);
      }
    }

    return { basic };
  }

  // Health check
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    latency: number;
    error?: string;
  }> {
    const start = Date.now();
    
    try {
      if (this.redis) {
        await this.redis.ping();
        const latency = Date.now() - start;
        
        return {
          status: latency < 100 ? 'healthy' : 'degraded',
          latency,
        };
      } else {
        // In-memory cache is always healthy
        return {
          status: 'healthy',
          latency: Date.now() - start,
        };
      }
    } catch (error: any) {
      return {
        status: 'unhealthy',
        latency: Date.now() - start,
        error: error.message,
      };
    }
  }

  // Cleanup expired keys manually (for in-memory fallback)
  async cleanup(): Promise<number> {
    try {
      if (this.redis) {
        // Redis handles expiration automatically
        return 0;
      } else {
        // Clean up expired keys in in-memory cache
        const keys = super.keys();
        let cleaned = 0;
        
        keys.forEach(key => {
          const ttl = super.getTtl(key);
          if (ttl !== undefined && ttl < Date.now()) {
            super.del(key);
            cleaned++;
          }
        });
        
        return cleaned;
      }
    } catch (error) {
      console.error('Cache cleanup error:', error);
      return 0;
    }
  }
}