import { RedisCacheService } from './RedisCacheService';

/**
 * Advanced cache manager with multiple strategies and layers
 */
export class CacheManager {
  private static instance: CacheManager;
  private cache: RedisCacheService;
  private strategies: Map<string, CacheStrategy> = new Map();
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    invalidations: 0,
    errors: 0,
  };

  private constructor() {
    this.cache = RedisCacheService.getInstance();
    this.initializeStrategies();
  }

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  private initializeStrategies() {
    // Product-specific caching strategy
    this.strategies.set('product', {
      defaultTTL: 600, // 10 minutes
      tags: ['products'],
      warmup: true,
      compression: true,
      invalidationRules: [
        'product:*',
        'categories:*', // Products affect category counts
        'search:*', // Products affect search results
      ],
    });

    // Category caching strategy
    this.strategies.set('category', {
      defaultTTL: 1800, // 30 minutes
      tags: ['categories'],
      warmup: true,
      compression: false,
      invalidationRules: [
        'category:*',
        'products:*', // Categories affect product listings
      ],
    });

    // User session caching
    this.strategies.set('user', {
      defaultTTL: 3600, // 1 hour
      tags: ['users'],
      warmup: false,
      compression: false,
      invalidationRules: ['user:*'],
    });

    // Cart caching strategy
    this.strategies.set('cart', {
      defaultTTL: 300, // 5 minutes
      tags: ['carts'],
      warmup: false,
      compression: false,
      invalidationRules: ['cart:*'],
    });

    // Search results caching
    this.strategies.set('search', {
      defaultTTL: 180, // 3 minutes
      tags: ['search'],
      warmup: false,
      compression: true,
      invalidationRules: ['search:*'],
    });

    // Admin dashboard stats
    this.strategies.set('stats', {
      defaultTTL: 900, // 15 minutes
      tags: ['stats'],
      warmup: true,
      compression: false,
      invalidationRules: ['stats:*'],
    });
  }

  /**
   * Get cached data with strategy
   */
  async get<T>(key: string, strategyName?: string): Promise<T | null> {
    try {
      const strategy = strategyName ? this.strategies.get(strategyName) : null;
      const result = await this.cache.getAdvanced(key);
      
      if (result) {
        this.metrics.hits++;
        
        // Decompress if needed
        let data = result.data;
        if (strategy?.compression && typeof data === 'string') {
          try {
            data = JSON.parse(data);
          } catch {
            // Data is not compressed or invalid JSON
          }
        }
        
        return data;
      } else {
        this.metrics.misses++;
        return null;
      }
    } catch (error) {
      this.metrics.errors++;
      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * Set cached data with strategy
   */
  async set<T>(
    key: string, 
    value: T, 
    strategyName?: string, 
    customTTL?: number
  ): Promise<boolean> {
    try {
      const strategy = strategyName ? this.strategies.get(strategyName) : null;
      let processedValue = value;
      
      // Compress if needed
      if (strategy?.compression && typeof value === 'object') {
        processedValue = JSON.stringify(value) as T;
      }
      
      const result = await this.cache.setAdvanced(key, processedValue, {
        ttl: customTTL || strategy?.defaultTTL || 300,
        tags: strategy?.tags,
      });
      
      if (result) {
        this.metrics.sets++;
      }
      
      return result;
    } catch (error) {
      this.metrics.errors++;
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Get or set cached data (cache-aside pattern)
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T>,
    strategyName?: string,
    customTTL?: number
  ): Promise<T> {
    let cached = await this.get<T>(key, strategyName);
    
    if (cached !== null) {
      return cached;
    }
    
    // Generate data
    const data = await factory();
    
    // Cache the result
    await this.set(key, data, strategyName, customTTL);
    
    return data;
  }

  /**
   * Invalidate cache with strategy-based rules
   */
  async invalidate(key: string, strategyName?: string): Promise<void> {
    try {
      const strategy = strategyName ? this.strategies.get(strategyName) : null;
      
      // Direct key invalidation
      await this.cache.del(key);
      
      // Strategy-based invalidation
      if (strategy?.invalidationRules) {
        for (const rule of strategy.invalidationRules) {
          await this.cache.invalidatePattern(rule);
        }
      }
      
      this.metrics.invalidations++;
    } catch (error) {
      this.metrics.errors++;
      console.error('Cache invalidation error:', error);
    }
  }

  /**
   * Invalidate by tags
   */
  async invalidateByTags(tags: string[]): Promise<void> {
    try {
      for (const tag of tags) {
        await this.cache.invalidateByTag(tag);
      }
      this.metrics.invalidations++;
    } catch (error) {
      this.metrics.errors++;
      console.error('Tag invalidation error:', error);
    }
  }

  /**
   * Warm up cache with predefined data
   */
  async warmup(): Promise<{ success: number; failed: number }> {
    const operations: Array<{
      key: string;
      factory: () => Promise<any>;
      ttl?: number;
      tags?: string[];
    }> = [];

    // Add warmup operations for strategies that support it
    for (const [name, strategy] of this.strategies) {
      if (strategy.warmup) {
        switch (name) {
          case 'product':
            operations.push({
              key: 'products:featured',
              factory: async () => {
                // Simulate featured products fetch
                return { data: [], total: 0 };
              },
              ttl: strategy.defaultTTL,
              tags: strategy.tags,
            });
            break;
            
          case 'category':
            operations.push({
              key: 'categories:tree',
              factory: async () => {
                // Simulate category tree fetch
                return [];
              },
              ttl: strategy.defaultTTL,
              tags: strategy.tags,
            });
            break;
            
          case 'stats':
            operations.push({
              key: 'stats:dashboard',
              factory: async () => {
                // Simulate dashboard stats fetch
                return {
                  products: { total: 0, active: 0 },
                  orders: { total: 0, pending: 0 },
                  users: { total: 0, active: 0 },
                };
              },
              ttl: strategy.defaultTTL,
              tags: strategy.tags,
            });
            break;
        }
      }
    }

    return this.cache.warmCache(operations);
  }

  /**
   * Get cache metrics and statistics
   */
  async getMetrics(): Promise<{
    performance: CacheMetrics;
    hitRate: number;
    detailed: any;
    health: any;
  }> {
    const total = this.metrics.hits + this.metrics.misses;
    const hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;
    
    return {
      performance: { ...this.metrics },
      hitRate: Math.round(hitRate * 100) / 100,
      detailed: await this.cache.getDetailedStats(),
      health: await this.cache.healthCheck(),
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      invalidations: 0,
      errors: 0,
    };
  }

  /**
   * Bulk operations for better performance
   */
  async mset(
    items: Array<{
      key: string;
      value: any;
      strategyName?: string;
      ttl?: number;
    }>
  ): Promise<boolean> {
    const processedItems = items.map(item => {
      const strategy = item.strategyName ? this.strategies.get(item.strategyName) : null;
      let processedValue = item.value;
      
      // Compress if needed
      if (strategy?.compression && typeof item.value === 'object') {
        processedValue = JSON.stringify(item.value);
      }
      
      return {
        key: item.key,
        value: processedValue,
        ttl: item.ttl || strategy?.defaultTTL || 300,
        tags: strategy?.tags,
      };
    });

    return this.cache.msetAdvanced(processedItems);
  }

  /**
   * Cleanup expired keys and optimize cache
   */
  async cleanup(): Promise<{
    cleaned: number;
    optimized: boolean;
  }> {
    const cleaned = await this.cache.cleanup();
    
    // Additional optimization logic
    const metrics = await this.getMetrics();
    let optimized = false;
    
    // If hit rate is too low, suggest cache warming
    if (metrics.hitRate < 50 && this.metrics.hits + this.metrics.misses > 100) {
      console.log('🔥 Cache hit rate is low, consider warming up cache');
      optimized = true;
    }
    
    return { cleaned, optimized };
  }
}

interface CacheStrategy {
  defaultTTL: number;
  tags: string[];
  warmup: boolean;
  compression: boolean;
  invalidationRules: string[];
}

interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  invalidations: number;
  errors: number;
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();