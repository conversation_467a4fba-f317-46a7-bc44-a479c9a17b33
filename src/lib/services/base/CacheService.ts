import NodeCache from 'node-cache';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  checkperiod?: number; // Period for automatic delete check in seconds
}

export class CacheService {
  private static instance: CacheService;
  private cache: NodeCache;

  private constructor(options: CacheOptions = {}) {
    this.cache = new NodeCache({
      stdTTL: options.ttl || 300, // 5 minutes default
      checkperiod: options.checkperiod || 60, // Check every minute
      useClones: false, // Better performance
    });

    // Setup cache event listeners
    this.cache.on('set', (key, value) => {
      console.log(`Cache SET: ${key}`);
    });

    this.cache.on('del', (key, value) => {
      console.log(`Cache DEL: ${key}`);
    });

    this.cache.on('expired', (key, value) => {
      console.log(`Cache EXPIRED: ${key}`);
    });
  }

  static getInstance(options?: CacheOptions): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService(options);
    }
    return CacheService.instance;
  }

  set<T>(key: string, value: T, ttl?: number): boolean {
    return this.cache.set(key, value, ttl || 0);
  }

  get<T>(key: string): T | undefined {
    return this.cache.get<T>(key);
  }

  mget<T>(keys: string[]): { [key: string]: T | undefined } {
    return this.cache.mget<T>(keys);
  }

  mset<T>(keyValuePairs: Array<{ key: string; val: T; ttl?: number }>): boolean {
    return this.cache.mset(keyValuePairs);
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  del(keys: string | string[]): number {
    return this.cache.del(keys);
  }

  take<T>(key: string): T | undefined {
    return this.cache.take<T>(key);
  }

  flush(): void {
    this.cache.flushAll();
  }

  keys(): string[] {
    return this.cache.keys();
  }

  getTtl(key: string): number | undefined {
    return this.cache.getTtl(key);
  }

  getStats(): NodeCache.Stats {
    return this.cache.getStats();
  }

  // Utility methods for common cache patterns
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T> | T,
    ttl?: number
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== undefined) {
      return cached;
    }

    const value = await factory();
    this.set(key, value, ttl);
    return value;
  }

  async invalidatePattern(pattern: string): Promise<number> {
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    const keys = this.keys().filter(key => regex.test(key));
    return this.del(keys);
  }

  createKey(...parts: (string | number)[]): string {
    return parts.join(':');
  }

  // Cache keys for common entities
  static KEYS = {
    PRODUCT: (id: string) => `product:${id}`,
    PRODUCTS_LIST: (params: string) => `products:list:${params}`,
    CATEGORY: (id: string) => `category:${id}`,
    CATEGORIES_LIST: () => 'categories:list',
    USER: (id: string) => `user:${id}`,
    CART: (userId: string) => `cart:${userId}`,
    WISHLIST: (userId: string) => `wishlist:${userId}`,
    SETTINGS: () => 'settings:all',
    BRANDS: () => 'brands:list',
    ATTRIBUTES: () => 'attributes:list',
  } as const;
}