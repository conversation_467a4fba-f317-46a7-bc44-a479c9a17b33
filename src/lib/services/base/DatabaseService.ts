import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

export class DatabaseService {
  private static instance: DatabaseService;
  
  private constructor() {}

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  get client() {
    return prisma;
  }

  async executeRawQuery<T = any>(query: string, params?: any[]): Promise<T[]> {
    return prisma.$queryRawUnsafe(query, ...(params || []));
  }

  async executeRawUnsafe<T = any>(query: Prisma.Sql): Promise<T[]> {
    return prisma.$queryRaw(query);
  }

  async transaction<T>(operations: (tx: typeof prisma) => Promise<T>): Promise<T> {
    return prisma.$transaction(operations);
  }

  async batchOperations<T>(operations: Prisma.PrismaPromise<T>[]): Promise<T[]> {
    return prisma.$transaction(operations);
  }

  async disconnect(): Promise<void> {
    await prisma.$disconnect();
  }

  async connect(): Promise<void> {
    await prisma.$connect();
  }

  async healthCheck(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  async getTableInfo(tableName: string): Promise<any[]> {
    return this.executeRawQuery(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);
  }

  async getTableStats(tableName: string): Promise<{ count: number; size: string }> {
    const [countResult, sizeResult] = await Promise.all([
      this.executeRawQuery(`SELECT COUNT(*) as count FROM "${tableName}"`),
      this.executeRawQuery(`
        SELECT pg_size_pretty(pg_total_relation_size($1)) as size
      `, [tableName])
    ]);

    return {
      count: parseInt(countResult[0]?.count || '0'),
      size: sizeResult[0]?.size || '0 bytes'
    };
  }
}