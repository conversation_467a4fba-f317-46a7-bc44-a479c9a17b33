import { prisma } from '@/lib/prisma';
import { PaginatedResponse, ApiResponse } from '@/types';

export interface FindManyParams {
  where?: any;
  include?: any;
  select?: any;
  orderBy?: any;
  skip?: number;
  take?: number;
}

export interface CreateParams<T> {
  data: T;
  include?: any;
  select?: any;
}

export interface UpdateParams<T> {
  where: any;
  data: Partial<T>;
  include?: any;
  select?: any;
}

export interface DeleteParams {
  where: any;
}

export abstract class BaseService<TModel, TCreateInput, TUpdateInput> {
  protected abstract model: any;
  protected abstract defaultInclude?: any;
  protected abstract defaultSelect?: any;

  async findMany(params: FindManyParams = {}): Promise<TModel[]> {
    const { include = this.defaultInclude, select = this.defaultSelect, ...rest } = params;
    
    return this.model.findMany({
      ...rest,
      include: include || undefined,
      select: select || undefined,
    });
  }

  async findManyPaginated(
    params: FindManyParams & { page?: number; limit?: number } = {}
  ): Promise<PaginatedResponse<TModel>> {
    const { page = 1, limit = 10, ...findParams } = params;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.findMany({ ...findParams, skip, take: limit }),
      this.count({ where: findParams.where }),
    ]);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1,
      },
    };
  }

  async findUnique(params: { where: any; include?: any; select?: any }): Promise<TModel | null> {
    const { include = this.defaultInclude, select = this.defaultSelect, ...rest } = params;
    
    return this.model.findUnique({
      ...rest,
      include: include || undefined,
      select: select || undefined,
    });
  }

  async findFirst(params: FindManyParams = {}): Promise<TModel | null> {
    const { include = this.defaultInclude, select = this.defaultSelect, ...rest } = params;
    
    return this.model.findFirst({
      ...rest,
      include: include || undefined,
      select: select || undefined,
    });
  }

  async create(params: CreateParams<TCreateInput>): Promise<TModel> {
    const { include = this.defaultInclude, select = this.defaultSelect, ...rest } = params;
    
    return this.model.create({
      ...rest,
      include: include || undefined,
      select: select || undefined,
    });
  }

  async createMany(data: TCreateInput[]): Promise<{ count: number }> {
    return this.model.createMany({ data });
  }

  async update(params: UpdateParams<TUpdateInput>): Promise<TModel> {
    const { include = this.defaultInclude, select = this.defaultSelect, ...rest } = params;
    
    return this.model.update({
      ...rest,
      include: include || undefined,
      select: select || undefined,
    });
  }

  async updateMany(params: { where: any; data: Partial<TUpdateInput> }): Promise<{ count: number }> {
    return this.model.updateMany(params);
  }

  async delete(params: DeleteParams): Promise<TModel> {
    return this.model.delete(params);
  }

  async deleteMany(params: { where: any }): Promise<{ count: number }> {
    return this.model.deleteMany(params);
  }

  async count(params: { where?: any } = {}): Promise<number> {
    return this.model.count(params);
  }

  async exists(where: any): Promise<boolean> {
    const count = await this.count({ where });
    return count > 0;
  }

  protected createSuccessResponse<T>(data: T, message?: string): ApiResponse<T> {
    return {
      success: true,
      data,
      message: message || 'Operation completed successfully',
    };
  }

  protected createErrorResponse(message: string, error?: any): ApiResponse<null> {
    return {
      success: false,
      data: null,
      message,
      error: error?.message || error,
    };
  }

  protected async executeInTransaction<T>(
    operations: (tx: typeof prisma) => Promise<T>
  ): Promise<T> {
    return prisma.$transaction(operations);
  }
}