import { BaseService, CacheService } from './base';
import { prisma } from '@/lib/prisma';
import { Category, CategoryCreateRequest, CategoryUpdateRequest, CategoryListParams } from '@/types/core';
import { Prisma } from '@prisma/client';

interface CategoryWithRelations extends Category {
  parent?: Category;
  children?: Category[];
  products?: any[];
  _count?: {
    products: number;
    children: number;
  };
}

export class CategoryService extends BaseService<CategoryWithRelations, CategoryCreateRequest, CategoryUpdateRequest> {
  protected model = prisma.category;
  protected defaultInclude = {
    parent: true,
    children: {
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' },
    },
    _count: {
      select: {
        products: { where: { status: 'ACTIVE' } },
        children: { where: { isActive: true } },
      },
    },
  };

  private cache = CacheService.getInstance();
  private static instance: CategoryService;

  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }

  async getCategories(params: CategoryListParams = {}) {
    const cacheKey = this.cache.createKey('categories', JSON.stringify(params));
    
    return this.cache.getOrSet(cacheKey, async () => {
      const where: Prisma.CategoryWhereInput = {
        ...(params.search && {
          OR: [
            { name: { contains: params.search, mode: 'insensitive' } },
            { description: { contains: params.search, mode: 'insensitive' } },
          ],
        }),
        ...(params.parentId !== undefined && { parentId: params.parentId }),
        ...(params.isActive !== undefined && { isActive: params.isActive }),
      };

      const orderBy: Prisma.CategoryOrderByWithRelationInput = {
        sortOrder: 'asc',
        name: 'asc',
      };

      if (params.includeChildren) {
        return this.findMany({
          where,
          orderBy,
          include: {
            ...this.defaultInclude,
            children: {
              where: { isActive: true },
              orderBy: { sortOrder: 'asc' },
              include: {
                _count: {
                  select: {
                    products: { where: { status: 'ACTIVE' } },
                  },
                },
              },
            },
          },
        });
      }

      return this.findManyPaginated({
        where,
        orderBy,
        page: params.page,
        limit: params.limit,
      });
    }, 900); // Cache for 15 minutes
  }

  async getCategory(id: string) {
    const cacheKey = CacheService.KEYS.CATEGORY(id);
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findUnique({ where: { id } });
    }, 1800); // Cache for 30 minutes
  }

  async getCategoryBySlug(slug: string) {
    const cacheKey = this.cache.createKey('category', 'slug', slug);
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findUnique({ where: { slug } });
    }, 1800);
  }

  async getRootCategories() {
    const cacheKey = 'categories:root';
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findMany({
        where: { 
          parentId: null,
          isActive: true,
        },
        orderBy: { sortOrder: 'asc' },
      });
    }, 1800);
  }

  async getCategoryTree() {
    const cacheKey = 'categories:tree';
    
    return this.cache.getOrSet(cacheKey, async () => {
      const categories = await this.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' },
        include: {
          children: {
            where: { isActive: true },
            orderBy: { sortOrder: 'asc' },
            include: {
              children: {
                where: { isActive: true },
                orderBy: { sortOrder: 'asc' },
              },
            },
          },
        },
      });

      return categories.filter(cat => !cat.parentId);
    }, 3600); // Cache for 1 hour
  }

  async createCategory(data: CategoryCreateRequest) {
    try {
      const category = await this.create({ data });
      
      // Invalidate cache
      await this.cache.invalidatePattern('categories:*');
      
      return this.createSuccessResponse(category, 'Category created successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to create category', error);
    }
  }

  async updateCategory(id: string, data: CategoryUpdateRequest) {
    try {
      const category = await this.update({ where: { id }, data });
      
      // Invalidate specific category and list caches
      this.cache.del([CacheService.KEYS.CATEGORY(id)]);
      await this.cache.invalidatePattern('categories:*');
      
      return this.createSuccessResponse(category, 'Category updated successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to update category', error);
    }
  }

  async deleteCategory(id: string) {
    try {
      // Check if category has children or products
      const category = await this.findUnique({
        where: { id },
        include: {
          children: true,
          products: true,
        },
      });

      if (!category) {
        return this.createErrorResponse('Category not found');
      }

      if (category.children && category.children.length > 0) {
        return this.createErrorResponse('Cannot delete category with subcategories');
      }

      if (category.products && category.products.length > 0) {
        return this.createErrorResponse('Cannot delete category with products');
      }

      await this.delete({ where: { id } });
      
      // Invalidate caches
      this.cache.del([CacheService.KEYS.CATEGORY(id)]);
      await this.cache.invalidatePattern('categories:*');
      
      return this.createSuccessResponse(null, 'Category deleted successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to delete category', error);
    }
  }

  async reorderCategories(categoryUpdates: Array<{ id: string; sortOrder: number }>) {
    try {
      await this.executeInTransaction(async (tx) => {
        for (const update of categoryUpdates) {
          await tx.category.update({
            where: { id: update.id },
            data: { sortOrder: update.sortOrder },
          });
        }
      });

      // Invalidate cache
      await this.cache.invalidatePattern('categories:*');
      
      return this.createSuccessResponse(null, 'Categories reordered successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to reorder categories', error);
    }
  }

  // Utility methods
  static buildCategoryPath(category: CategoryWithRelations): string[] {
    const path: string[] = [];
    let current = category;
    
    while (current) {
      path.unshift(current.name);
      current = current.parent as CategoryWithRelations;
    }
    
    return path;
  }

  static getCategoryDepth(category: CategoryWithRelations): number {
    let depth = 0;
    let current = category;
    
    while (current?.parent) {
      depth++;
      current = current.parent as CategoryWithRelations;
    }
    
    return depth;
  }
}

// Export singleton instance
export const categoryService = CategoryService.getInstance();