import { BaseService } from '@/lib/services/base';
import { CacheService } from '@/lib/services/base';
import { prisma } from '@/lib/prisma';
import { Cart, CartItem, CartAddItemRequest, CartUpdateItemRequest } from '@/types/core';
import { Prisma } from '@prisma/client';

export interface CartSummary {
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  itemCount: number;
  totalQuantity: number;
}

interface CartWithRelations extends Cart {
  items?: Array<{
    id: string;
    quantity: number;
    product: {
      id: string;
      name: string;
      price: number;
      salePrice?: number;
      stock: number;
      status: string;
      images?: any[];
    };
  }>;
}

export class CartService extends BaseService<CartWithRelations, any, any> {
  protected model = prisma.cart;
  protected defaultInclude = {
    items: {
      include: {
        product: {
          include: {
            images: {
              include: {
                media: true,
              },
              where: {
                isMain: true,
              },
              take: 1,
            },
          },
        },
      },
    },
  };

  private cache = CacheService.getInstance();
  private static instance: CartService;

  static getInstance(): CartService {
    if (!CartService.instance) {
      CartService.instance = new CartService();
    }
    return CartService.instance;
  }
  async getCart(userId: string) {
    const cacheKey = CacheService.KEYS.CART(userId);
    
    return this.cache.getOrSet(cacheKey, async () => {
      let cart = await this.findFirst({ where: { userId } });
      
      if (!cart) {
        cart = await this.create({ data: { userId } });
      }
      
      return cart;
    }, 300); // Cache for 5 minutes
  }

  async addToCart(userId: string, data: CartAddItemRequest) {
    try {
      const result = await this.executeInTransaction(async (tx) => {
        // Get or create cart
        let cart = await tx.cart.findFirst({ where: { userId } });
        if (!cart) {
          cart = await tx.cart.create({ data: { userId } });
        }

        // Check if item already exists
        const existingItem = await tx.cartItem.findFirst({
          where: {
            cartId: cart.id,
            productId: data.productId,
          },
        });

        if (existingItem) {
          // Update quantity
          await tx.cartItem.update({
            where: { id: existingItem.id },
            data: { quantity: existingItem.quantity + data.quantity },
          });
        } else {
          // Create new item
          await tx.cartItem.create({
            data: {
              cartId: cart.id,
              productId: data.productId,
              quantity: data.quantity,
            },
          });
        }

        return cart;
      });

      // Invalidate cache
      this.cache.del([CacheService.KEYS.CART(userId)]);
      
      return this.createSuccessResponse(result, 'Item added to cart successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to add item to cart', error);
    }
  }

  async updateCartItem(userId: string, itemId: string, data: CartUpdateItemRequest) {
    try {
      await prisma.cartItem.update({
        where: { id: itemId },
        data: { quantity: data.quantity },
      });

      // Invalidate cache
      this.cache.del([CacheService.KEYS.CART(userId)]);
      
      return this.createSuccessResponse(null, 'Cart item updated successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to update cart item', error);
    }
  }

  async removeFromCart(userId: string, itemId: string) {
    try {
      await prisma.cartItem.delete({ where: { id: itemId } });
      
      // Invalidate cache
      this.cache.del([CacheService.KEYS.CART(userId)]);
      
      return this.createSuccessResponse(null, 'Item removed from cart successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to remove item from cart', error);
    }
  }

  async clearCart(userId: string) {
    try {
      const cart = await this.findFirst({ where: { userId } });
      if (cart) {
        await prisma.cartItem.deleteMany({ where: { cartId: cart.id } });
      }
      
      // Invalidate cache
      this.cache.del([CacheService.KEYS.CART(userId)]);
      
      return this.createSuccessResponse(null, 'Cart cleared successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to clear cart', error);
    }
  }

  async applyCoupon(userId: string, code: string) {
    try {
      // TODO: Implement coupon validation logic
      const cart = await this.findFirst({ where: { userId } });
      if (!cart) {
        return this.createErrorResponse('Cart not found');
      }

      // Apply coupon logic here
      
      return this.createSuccessResponse(cart, 'Coupon applied successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to apply coupon', error);
    }
  }

  async removeCoupon(userId: string) {
    try {
      const cart = await this.findFirst({ where: { userId } });
      if (!cart) {
        return this.createErrorResponse('Cart not found');
      }

      // Remove coupon logic here
      
      return this.createSuccessResponse(cart, 'Coupon removed successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to remove coupon', error);
    }
  }

  static calculateCartSummary(
    cart: CartWithRelations,
    shippingFee: number = 0,
    taxRate: number = 0
  ): CartSummary {
    if (!cart || !cart.items || cart.items.length === 0) {
      return {
        subtotal: 0,
        shipping: 0,
        tax: 0,
        total: 0,
        itemCount: 0,
        totalQuantity: 0,
      };
    }

    const subtotal = cart.items.reduce((sum, item) => {
      const price = item.product.salePrice || item.product.price;
      return sum + price * item.quantity;
    }, 0);

    const tax = subtotal * taxRate;
    const total = subtotal + shippingFee + tax;
    const itemCount = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    const totalQuantity = itemCount; // Same as itemCount for now

    return {
      subtotal,
      shipping: shippingFee,
      tax,
      total,
      itemCount,
      totalQuantity,
    };
  }

  static isProductInCart(cart: CartWithRelations | null, productId: string): boolean {
    if (!cart || !cart.items) return false;
    return cart.items.some((item) => item.productId === productId);
  }

  static getCartItem(cart: CartWithRelations | null, productId: string): any | null {
    if (!cart || !cart.items) return null;
    return cart.items.find((item) => item.productId === productId) || null;
  }

  static getCartItemQuantity(cart: CartWithRelations | null, productId: string): number {
    const item = this.getCartItem(cart, productId);
    return item ? item.quantity : 0;
  }

  static validateCartItemStock(item: any): {
    isValid: boolean;
    maxQuantity: number;
    message?: string;
  } {
    const { product, quantity } = item;

    if (product.status !== "ACTIVE") {
      return {
        isValid: false,
        maxQuantity: 0,
        message: "Sản phẩm không còn khả dụng",
      };
    }

    if (product.stock === 0) {
      return {
        isValid: false,
        maxQuantity: 0,
        message: "Sản phẩm đã hết hàng",
      };
    }

    if (quantity > product.stock) {
      return {
        isValid: false,
        maxQuantity: product.stock,
        message: `Chỉ còn ${product.stock} sản phẩm trong kho`,
      };
    }

    return {
      isValid: true,
      maxQuantity: product.stock,
    };
  }

  static validateCart(cart: CartWithRelations): {
    isValid: boolean;
    invalidItems: Array<{
      item: any;
      reason: string;
      maxQuantity: number;
    }>;
  } {
    if (!cart || !cart.items || cart.items.length === 0) {
      return { isValid: true, invalidItems: [] };
    }

    const invalidItems: Array<{
      item: any;
      reason: string;
      maxQuantity: number;
    }> = [];

    cart.items.forEach((item) => {
      const validation = this.validateCartItemStock(item);
      if (!validation.isValid) {
        invalidItems.push({
          item,
          reason: validation.message || "Sản phẩm không hợp lệ",
          maxQuantity: validation.maxQuantity,
        });
      }
    });

    return {
      isValid: invalidItems.length === 0,
      invalidItems,
    };
  }

  // Local storage methods for guest cart
  static getGuestCart(): any[] {
    if (typeof window === "undefined") return [];

    try {
      const stored = localStorage.getItem("guestCart");
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  static saveGuestCart(items: any[]): void {
    if (typeof window === "undefined") return;

    try {
      localStorage.setItem("guestCart", JSON.stringify(items));
    } catch (error) {
      console.error("Error saving guest cart:", error);
    }
  }

  static addToGuestCart(productId: string, quantity: number): void {
    const items = this.getGuestCart();
    const existingIndex = items.findIndex(
      (item) => item.productId === productId
    );

    if (existingIndex >= 0) {
      items[existingIndex].quantity += quantity;
    } else {
      items.push({
        id: `guest-${Date.now()}`,
        cartId: 'guest',
        productId,
        quantity,
        product: {} as any,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    this.saveGuestCart(items);
  }

  static removeFromGuestCart(productId: string): void {
    const items = this.getGuestCart();
    const filtered = items.filter((item) => item.productId !== productId);
    this.saveGuestCart(filtered);
  }

  static clearGuestCart(): void {
    if (typeof window === "undefined") return;
    localStorage.removeItem("guestCart");
  }

  async mergeGuestCart(userId: string) {
    const guestItems = CartService.getGuestCart();
    if (guestItems.length === 0) return null;

    try {
      for (const item of guestItems) {
        await this.addToCart(userId, {
          productId: item.productId,
          quantity: item.quantity,
        });
      }

      CartService.clearGuestCart();
      return this.getCart(userId);
    } catch (error) {
      console.error('Error merging guest cart:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const cartService = CartService.getInstance();
export default CartService;
