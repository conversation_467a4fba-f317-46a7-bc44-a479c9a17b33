// UPDATED SERVICE EXPORTS - New Architecture

// Export the service manager as the primary interface
export { ServiceManager, serviceManager } from './ServiceManager';

// Export individual service classes for advanced usage
export { ProductService } from './product-service';
export { CartService } from './cart-service';
export { CategoryService } from './CategoryService';
export { UserService } from './UserService';

// Temporary OrderService export for compatibility
export const OrderService = {
  getOrders: async () => ({ data: [], total: 0 }),
  getOrder: async (id: string) => null,
  createOrder: async (data: any) => ({ id: 'temp' }),
  updateOrder: async (id: string, data: any) => ({ id }),
  deleteOrder: async (id: string) => true,
};

// Export base services
export { BaseService, DatabaseService, CacheService } from './base';

// Export service instances for direct usage
export {
  productService,
  cartService,
  categoryService,
  userService,
} from './ServiceManager';

// Export types
export type { FindManyParams, CreateParams, UpdateParams, DeleteParams, CacheOptions } from './base';

// Legacy service types - redirect to core types
export type {
  ProductListParams,
} from '@/types/core';

export type {
  CategoryListParams,
} from '@/types/core';

export type {
  CartAddItemRequest as AddToCartData,
  CartUpdateItemRequest as UpdateCartItemData,
} from '@/types/core';

export type {
  UserListParams,
  UserCreateRequest as UserProfileData,
  Address as AddressData,
  ChangePasswordRequest as ChangePasswordData,
} from '@/types/core';

// Temporary order types for compatibility
export type OrderListParams = {
  page?: number;
  limit?: number;
  status?: string;
  userId?: string;
};

export type CreateOrderData = {
  items: Array<{ productId: string; quantity: number; price: number }>;
  shippingAddress: any;
  paymentMethod: string;
};

export type OrderStats = {
  total: number;
  pending: number;
  completed: number;
  cancelled: number;
};

export type OrderTimeline = Array<{
  status: string;
  timestamp: string;
  note?: string;
}>;

// Legacy API client exports
export { api, apiClient, ApiError } from "../api-client";
export type { RequestConfig, CacheConfig } from "../api-client";

// Migration guide
console.info(
  '📦 NS Shop Services: Using new architecture with ServiceManager. ' +
  'For best performance, use: import { serviceManager } from "@/lib/services"'
);