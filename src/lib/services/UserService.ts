import { BaseService, CacheService } from './base';
import { prisma } from '@/lib/prisma';
import { User, UserCreateRequest, UserUpdateRequest, UserListParams } from '@/types/core';
import { Prisma } from '@prisma/client';
import bcrypt from 'bcryptjs';

interface UserWithRelations extends User {
  orders?: any[];
  addresses?: any[];
  reviews?: any[];
  cart?: any;
  wishlistItems?: any[];
  _count?: {
    orders: number;
    reviews: number;
    wishlistItems: number;
  };
}

export class UserService extends BaseService<UserWithRelations, UserCreateRequest, UserUpdateRequest> {
  protected model = prisma.user;
  protected defaultInclude = {
    addresses: {
      orderBy: { createdAt: 'desc' },
    },
    _count: {
      select: {
        orders: true,
        reviews: true,
        wishlistItems: true,
      },
    },
  };

  private cache = CacheService.getInstance();
  private static instance: UserService;

  static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  async getUsers(params: UserListParams = {}) {
    const cacheKey = this.cache.createKey('users', JSON.stringify(params));
    
    return this.cache.getOrSet(cacheKey, async () => {
      const where: Prisma.UserWhereInput = {
        ...(params.search && {
          OR: [
            { name: { contains: params.search, mode: 'insensitive' } },
            { email: { contains: params.search, mode: 'insensitive' } },
          ],
        }),
        ...(params.isActive !== undefined && { isActive: params.isActive }),
        ...(params.hasOrders && {
          orders: {
            some: {},
          },
        }),
        ...(params.startDate && {
          createdAt: { gte: new Date(params.startDate) },
        }),
        ...(params.endDate && {
          createdAt: { lte: new Date(params.endDate) },
        }),
      };

      const orderBy: Prisma.UserOrderByWithRelationInput = {};
      if (params.sortBy) {
        switch (params.sortBy) {
          case 'name':
            orderBy.name = params.sortOrder || 'asc';
            break;
          case 'email':
            orderBy.email = params.sortOrder || 'asc';
            break;
          case 'createdAt':
            orderBy.createdAt = params.sortOrder || 'desc';
            break;
          default:
            orderBy.createdAt = 'desc';
        }
      } else {
        orderBy.createdAt = 'desc';
      }

      return this.findManyPaginated({
        where,
        orderBy,
        page: params.page,
        limit: params.limit,
      });
    }, 300); // Cache for 5 minutes
  }

  async getUser(id: string) {
    const cacheKey = CacheService.KEYS.USER(id);
    
    return this.cache.getOrSet(cacheKey, async () => {
      const user = await this.findUnique({ 
        where: { id },
        include: {
          ...this.defaultInclude,
          orders: {
            orderBy: { createdAt: 'desc' },
            take: 5, // Recent orders
          },
          reviews: {
            orderBy: { createdAt: 'desc' },
            take: 5, // Recent reviews
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          },
        },
      });

      if (user) {
        // Remove password from response
        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword;
      }

      return null;
    }, 600); // Cache for 10 minutes
  }

  async getUserByEmail(email: string) {
    const cacheKey = this.cache.createKey('user', 'email', email);
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findUnique({ where: { email } });
    }, 600);
  }

  async createUser(data: UserCreateRequest) {
    try {
      // Hash password
      const hashedPassword = await bcrypt.hash(data.password, 12);
      
      const user = await this.create({
        data: {
          ...data,
          password: hashedPassword,
        },
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      
      // Invalidate users cache
      await this.cache.invalidatePattern('users:*');
      
      return this.createSuccessResponse(userWithoutPassword, 'User created successfully');
    } catch (error: any) {
      if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
        return this.createErrorResponse('Email already exists');
      }
      return this.createErrorResponse('Failed to create user', error);
    }
  }

  async updateUser(id: string, data: UserUpdateRequest) {
    try {
      const updateData: any = { ...data };
      
      // Hash password if provided
      if (data.password) {
        updateData.password = await bcrypt.hash(data.password, 12);
      }

      const user = await this.update({ 
        where: { id }, 
        data: updateData,
      });

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      
      // Invalidate specific user and list caches
      this.cache.del([CacheService.KEYS.USER(id)]);
      await this.cache.invalidatePattern('users:*');
      
      return this.createSuccessResponse(userWithoutPassword, 'User updated successfully');
    } catch (error: any) {
      if (error.code === 'P2002' && error.meta?.target?.includes('email')) {
        return this.createErrorResponse('Email already exists');
      }
      return this.createErrorResponse('Failed to update user', error);
    }
  }

  async deleteUser(id: string) {
    try {
      // Check if user has orders
      const user = await this.findUnique({
        where: { id },
        include: {
          orders: true,
        },
      });

      if (!user) {
        return this.createErrorResponse('User not found');
      }

      if (user.orders && user.orders.length > 0) {
        // Soft delete - deactivate user instead of hard delete
        await this.update({
          where: { id },
          data: { isActive: false },
        });
        
        this.cache.del([CacheService.KEYS.USER(id)]);
        await this.cache.invalidatePattern('users:*');
        
        return this.createSuccessResponse(null, 'User deactivated successfully');
      }

      // Hard delete if no orders
      await this.delete({ where: { id } });
      
      // Invalidate caches
      this.cache.del([CacheService.KEYS.USER(id)]);
      await this.cache.invalidatePattern('users:*');
      
      return this.createSuccessResponse(null, 'User deleted successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to delete user', error);
    }
  }

  async validateUserCredentials(email: string, password: string) {
    try {
      const user = await this.findUnique({
        where: { email, isActive: true },
      });

      if (!user) {
        return { valid: false, user: null };
      }

      const isValid = await bcrypt.compare(password, user.password);
      
      if (isValid) {
        const { password: _, ...userWithoutPassword } = user;
        return { valid: true, user: userWithoutPassword };
      }

      return { valid: false, user: null };
    } catch (error) {
      console.error('Error validating user credentials:', error);
      return { valid: false, user: null };
    }
  }

  async updateLastLoginAt(id: string) {
    try {
      await this.update({
        where: { id },
        data: { updatedAt: new Date() }, // Using updatedAt as lastLoginAt
      });

      // Invalidate user cache
      this.cache.del([CacheService.KEYS.USER(id)]);
    } catch (error) {
      console.error('Error updating last login:', error);
    }
  }

  async getUserStats() {
    const cacheKey = 'users:stats';
    
    return this.cache.getOrSet(cacheKey, async () => {
      const [total, active, newThisMonth] = await Promise.all([
        this.count(),
        this.count({ where: { isActive: true } }),
        this.count({
          where: {
            createdAt: {
              gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
            },
          },
        }),
      ]);

      return {
        total,
        active,
        newThisMonth,
      };
    }, 900); // Cache for 15 minutes
  }

  // Utility methods
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static isValidPassword(password: string): boolean {
    // At least 8 characters, at least one letter and one number
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/;
    return passwordRegex.test(password);
  }

  static generateTempPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 12; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// Export singleton instance
export const userService = UserService.getInstance();