import { BaseService } from '@/lib/services/base';
import { CacheService } from '@/lib/services/base';
import { prisma } from '@/lib/prisma';
import { Product, ProductCreateRequest, ProductUpdateRequest, ProductListParams } from '@/types/core';
import { Prisma } from '@prisma/client';

interface ProductWithRelations extends Product {
  category?: any;
  brand?: any;
  images?: any[];
  attributes?: any[];
  reviews?: any[];
}

export class ProductService extends BaseService<ProductWithRelations, ProductCreateRequest, ProductUpdateRequest> {
  protected model = prisma.product;
  protected defaultInclude = {
    category: true,
    brand: true,
    images: {
      include: {
        media: true,
      },
      orderBy: {
        sortOrder: 'asc' as const,
      },
    },
    attributes: {
      include: {
        attribute: true,
        attributeValue: true,
      },
    },
    _count: {
      select: {
        reviews: true,
        cartItems: true,
        wishlistItems: true,
      },
    },
  };

  private cache = CacheService.getInstance();
  private static instance: ProductService;

  static getInstance(): ProductService {
    if (!ProductService.instance) {
      ProductService.instance = new ProductService();
    }
    return ProductService.instance;
  }
  async getProducts(params: ProductListParams = {}) {
    const cacheKey = this.cache.createKey('products', JSON.stringify(params));
    
    return this.cache.getOrSet(cacheKey, async () => {
      const where: Prisma.ProductWhereInput = {
        ...(params.search && {
          OR: [
            { name: { contains: params.search, mode: 'insensitive' } },
            { description: { contains: params.search, mode: 'insensitive' } },
            { sku: { contains: params.search, mode: 'insensitive' } },
          ],
        }),
        ...(params.categoryId && { categoryId: params.categoryId }),
        ...(params.brandId && { brandId: params.brandId }),
        ...(params.status && { status: params.status }),
        ...(params.featured && { featured: true }),
        ...(params.minPrice && { price: { gte: params.minPrice } }),
        ...(params.maxPrice && { price: { lte: params.maxPrice } }),
        ...(params.tags && { tags: { hasSome: params.tags } }),
      };

      const orderBy: Prisma.ProductOrderByWithRelationInput = {};
      if (params.sortBy) {
        switch (params.sortBy) {
          case 'name':
            orderBy.name = params.sortOrder || 'asc';
            break;
          case 'price':
            orderBy.price = params.sortOrder || 'asc';
            break;
          case 'createdAt':
            orderBy.createdAt = params.sortOrder || 'desc';
            break;
          default:
            orderBy.createdAt = 'desc';
        }
      }

      return this.findManyPaginated({
        where,
        orderBy,
        page: params.page,
        limit: params.limit,
      });
    }, 300); // Cache for 5 minutes
  }

  async getProduct(id: string) {
    const cacheKey = CacheService.KEYS.PRODUCT(id);
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findUnique({ where: { id } });
    }, 600); // Cache for 10 minutes
  }

  async getProductBySlug(slug: string) {
    const cacheKey = this.cache.createKey('product', 'slug', slug);
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findUnique({ where: { slug } });
    }, 600);
  }

  async getFeaturedProducts(limit: number = 8) {
    const cacheKey = this.cache.createKey('products', 'featured', limit.toString());
    
    return this.cache.getOrSet(cacheKey, async () => {
      return this.findMany({
        where: { featured: true, status: 'ACTIVE' },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });
    }, 900); // Cache for 15 minutes
  }

  async getTrendingProducts(limit: number = 8) {
    const cacheKey = this.cache.createKey('products', 'trending', limit.toString());
    
    return this.cache.getOrSet(cacheKey, async () => {
      // Get products with highest average rating and recent activity
      return this.findMany({
        where: { 
          status: 'ACTIVE',
          reviews: {
            some: {},
          },
        },
        orderBy: [
          { reviews: { _count: 'desc' } },
          { createdAt: 'desc' },
        ],
        take: limit,
      });
    }, 600);
  }

  async getProductsByCategory(
    categoryId: string,
    params: Omit<ProductListParams, 'categoryId'> = {}
  ) {
    return this.getProducts({ ...params, categoryId });
  }

  async getProductsByBrand(
    brandId: string,
    params: Omit<ProductListParams, 'brandId'> = {}
  ) {
    return this.getProducts({ ...params, brandId });
  }

  async searchProducts(
    query: string,
    filters: Partial<ProductListParams> = {},
    page: number = 1,
    limit: number = 20
  ) {
    return this.getProducts({
      search: query,
      page,
      limit,
      ...filters,
    });
  }

  async getRelatedProducts(productId: string, limit: number = 4) {
    const cacheKey = this.cache.createKey('products', 'related', productId, limit.toString());
    
    return this.cache.getOrSet(cacheKey, async () => {
      const product = await this.findUnique({ 
        where: { id: productId },
        select: { categoryId: true, brandId: true, tags: true }
      });
      
      if (!product) return [];
      
      return this.findMany({
        where: {
          id: { not: productId },
          status: 'ACTIVE',
          OR: [
            { categoryId: product.categoryId },
            { brandId: product.brandId },
            { tags: { hasSome: product.tags } },
          ],
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });
    }, 1800); // Cache for 30 minutes
  }

  // Get recently viewed products (from localStorage)
  static getRecentlyViewed(): Product[] {
    if (typeof window === "undefined") return [];

    try {
      const stored = localStorage.getItem("recentlyViewed");
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  // Add product to recently viewed
  static addToRecentlyViewed(product: Product): void {
    if (typeof window === "undefined") return;

    try {
      const recent = this.getRecentlyViewed();
      const filtered = recent.filter((p) => p.id !== product.id);
      const updated = [product, ...filtered].slice(0, 10); // Keep last 10
      localStorage.setItem("recentlyViewed", JSON.stringify(updated));
    } catch (error) {
      console.error("Error saving to recently viewed:", error);
    }
  }

  // Clear recently viewed
  static clearRecentlyViewed(): void {
    if (typeof window === "undefined") return;
    localStorage.removeItem("recentlyViewed");
  }

  async createProduct(data: ProductCreateRequest) {
    try {
      const product = await this.create({ data });
      
      // Invalidate cache
      await this.cache.invalidatePattern('products:*');
      
      return this.createSuccessResponse(product, 'Product created successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to create product', error);
    }
  }

  async updateProduct(id: string, data: ProductUpdateRequest) {
    try {
      const product = await this.update({ where: { id }, data });
      
      // Invalidate specific product and list caches
      this.cache.del([CacheService.KEYS.PRODUCT(id)]);
      await this.cache.invalidatePattern('products:*');
      
      return this.createSuccessResponse(product, 'Product updated successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to update product', error);
    }
  }

  async deleteProduct(id: string) {
    try {
      await this.delete({ where: { id } });
      
      // Invalidate caches
      this.cache.del([CacheService.KEYS.PRODUCT(id)]);
      await this.cache.invalidatePattern('products:*');
      
      return this.createSuccessResponse(null, 'Product deleted successfully');
    } catch (error) {
      return this.createErrorResponse('Failed to delete product', error);
    }
  }

  // Utility methods
  static getProductPrice(product: Product): number {
    return product.salePrice || product.price;
  }

  static getProductDiscount(product: Product): number {
    if (!product.salePrice) return 0;
    return Math.round(
      ((product.price - product.salePrice) / product.price) * 100
    );
  }

  static isProductOnSale(product: Product): boolean {
    return !!product.salePrice && product.salePrice < product.price;
  }

  static isProductInStock(product: Product): boolean {
    return product.status === 'ACTIVE' && product.stock > 0;
  }

  static getProductMainImage(product: ProductWithRelations): string | null {
    if (product.images && product.images.length > 0) {
      const mainImage = product.images.find((img) => img.isMain);
      if (mainImage?.media?.url) return mainImage.media.url;
      return product.images[0]?.media?.url || null;
    }
    return null;
  }

  static getProductImages(product: ProductWithRelations): string[] {
    if (product.images && product.images.length > 0) {
      return product.images
        .sort((a, b) => a.sortOrder - b.sortOrder)
        .map((img) => img.media?.url)
        .filter(Boolean) as string[];
    }
    return [];
  }

  // Recently viewed products (localStorage methods)
  static getRecentlyViewed(): Product[] {
    if (typeof window === 'undefined') return [];

    try {
      const stored = localStorage.getItem('recentlyViewed');
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  static addToRecentlyViewed(product: Product): void {
    if (typeof window === 'undefined') return;

    try {
      const recent = this.getRecentlyViewed();
      const filtered = recent.filter((p) => p.id !== product.id);
      const updated = [product, ...filtered].slice(0, 10);
      localStorage.setItem('recentlyViewed', JSON.stringify(updated));
    } catch (error) {
      console.error('Error saving to recently viewed:', error);
    }
  }

  static clearRecentlyViewed(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem('recentlyViewed');
  }
}

// Export singleton instance for use throughout the app
export const productService = ProductService.getInstance();
export default ProductService;
