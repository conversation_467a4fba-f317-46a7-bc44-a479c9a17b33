import { ProductService, productService } from './product-service';
import { CartService, cartService } from './cart-service';
import { CategoryService, categoryService } from './CategoryService';
import { UserService, userService } from './UserService';
import { CacheService, DatabaseService } from './base';

/**
 * Central service manager that provides access to all application services
 * and manages their lifecycle, caching, and database connections.
 */
export class ServiceManager {
  private static instance: ServiceManager;
  private cacheService: CacheService;
  private databaseService: DatabaseService;
  
  // Service instances
  public readonly product: ProductService;
  public readonly cart: CartService;
  public readonly category: CategoryService;
  public readonly user: UserService;

  private constructor() {
    // Initialize core services
    this.cacheService = CacheService.getInstance();
    this.databaseService = DatabaseService.getInstance();
    
    // Initialize domain services
    this.product = productService;
    this.cart = cartService;
    this.category = categoryService;
    this.user = userService;
  }

  static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  /**
   * Get cache service instance
   */
  get cache(): CacheService {
    return this.cacheService;
  }

  /**
   * Get database service instance
   */
  get database(): DatabaseService {
    return this.databaseService;
  }

  /**
   * Initialize all services and their dependencies
   */
  async initialize(): Promise<void> {
    try {
      // Test database connection
      const isHealthy = await this.databaseService.healthCheck();
      if (!isHealthy) {
        throw new Error('Database connection failed');
      }

      console.log('✅ ServiceManager initialized successfully');
    } catch (error) {
      console.error('❌ ServiceManager initialization failed:', error);
      throw error;
    }
  }

  /**
   * Cleanup all services and close connections
   */
  async cleanup(): Promise<void> {
    try {
      // Clear all caches
      this.cacheService.flush();
      
      // Close database connections
      await this.databaseService.disconnect();
      
      console.log('✅ ServiceManager cleaned up successfully');
    } catch (error) {
      console.error('❌ ServiceManager cleanup failed:', error);
      throw error;
    }
  }

  /**
   * Clear all caches across all services
   */
  async clearAllCaches(): Promise<void> {
    this.cacheService.flush();
    console.log('🧹 All caches cleared');
  }

  /**
   * Get system health status
   */
  async getHealthStatus(): Promise<{
    database: boolean;
    cache: boolean;
    services: Record<string, boolean>;
  }> {
    const databaseHealth = await this.databaseService.healthCheck();
    const cacheHealth = this.cacheService.keys().length >= 0; // Basic cache check
    
    return {
      database: databaseHealth,
      cache: cacheHealth,
      services: {
        product: !!this.product,
        cart: !!this.cart,
        category: !!this.category,
        user: !!this.user,
      },
    };
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return this.cacheService.getStats();
  }

  /**
   * Execute a function within a database transaction across multiple services
   */
  async withTransaction<T>(
    operation: (services: {
      product: ProductService;
      cart: CartService;
      category: CategoryService;
      user: UserService;
    }) => Promise<T>
  ): Promise<T> {
    return this.databaseService.transaction(async (tx) => {
      // Pass services with transaction context
      return operation({
        product: this.product,
        cart: this.cart,
        category: this.category,
        user: this.user,
      });
    });
  }

  /**
   * Bulk operations across multiple services
   */
  async bulkInvalidateCache(patterns: string[]): Promise<void> {
    for (const pattern of patterns) {
      await this.cacheService.invalidatePattern(pattern);
    }
  }

  /**
   * Get database table statistics
   */
  async getTableStats(): Promise<Record<string, { count: number; size: string }>> {
    const tables = ['products', 'categories', 'users', 'orders', 'carts'];
    const stats: Record<string, { count: number; size: string }> = {};

    for (const table of tables) {
      try {
        stats[table] = await this.databaseService.getTableStats(table);
      } catch (error) {
        console.error(`Failed to get stats for table ${table}:`, error);
        stats[table] = { count: 0, size: '0 bytes' };
      }
    }

    return stats;
  }

  /**
   * Validate service dependencies and configuration
   */
  validateConfiguration(): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if all required services are initialized
    if (!this.product) errors.push('ProductService not initialized');
    if (!this.cart) errors.push('CartService not initialized');
    if (!this.category) errors.push('CategoryService not initialized');
    if (!this.user) errors.push('UserService not initialized');

    // Check database connection
    if (!this.databaseService) {
      errors.push('DatabaseService not initialized');
    }

    // Check cache service
    if (!this.cacheService) {
      warnings.push('CacheService not initialized - performance may be impacted');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }
}

// Export singleton instance
export const serviceManager = ServiceManager.getInstance();

// Export individual services for convenience
export {
  productService,
  cartService,
  categoryService,
  userService,
};

// Default export
export default ServiceManager;