import { NextRequest } from 'next/server';
import { Zod<PERSON>che<PERSON>, ZodError, z } from 'zod';

/**
 * Request validation middleware using Zod schemas
 */
export class ValidationMiddleware {
  /**
   * Validate request query parameters
   */
  static validateQuery<T>(req: NextRequest, schema: ZodSchema<T>): T {
    try {
      const url = new URL(req.url);
      const query = Object.fromEntries(url.searchParams.entries());
      
      // Transform string values to appropriate types
      const transformedQuery = this.transformQueryTypes(query);
      
      return schema.parse(transformedQuery);
    } catch (error) {
      if (error instanceof ZodError) {
        throw new ValidationError('Query validation failed', error.errors);
      }
      throw error;
    }
  }

  /**
   * Validate request body
   */
  static async validateBody<T>(req: NextRequest, schema: ZodSchema<T>): Promise<T> {
    try {
      const contentType = req.headers.get('content-type') || '';
      let body: any;

      if (contentType.includes('application/json')) {
        body = await req.json();
      } else if (contentType.includes('application/x-www-form-urlencoded')) {
        const formData = await req.formData();
        body = Object.fromEntries(formData.entries());
      } else if (contentType.includes('multipart/form-data')) {
        const formData = await req.formData();
        body = this.processFormData(formData);
      } else {
        throw new ValidationError('Unsupported content type', []);
      }

      return schema.parse(body);
    } catch (error) {
      if (error instanceof ZodError) {
        throw new ValidationError('Body validation failed', error.errors);
      }
      throw error;
    }
  }

  /**
   * Validate URL parameters
   */
  static validateParams<T>(req: NextRequest, schema: ZodSchema<T>, paramNames: string[] = ['id']): T {
    try {
      const url = new URL(req.url);
      const segments = url.pathname.split('/').filter(Boolean);
      
      // Extract parameters based on common patterns
      const params: Record<string, string> = {};
      
      // Simple parameter extraction
      if (paramNames.includes('id') && segments.length > 0) {
        params.id = segments[segments.length - 1];
      }
      
      if (paramNames.includes('slug')) {
        // Look for slug in the path
        const slugIndex = segments.findIndex(segment => !segment.match(/^[0-9a-f-]{36}$/)); // Not UUID
        if (slugIndex !== -1) {
          params.slug = segments[slugIndex];
        }
      }

      // Add more parameter extraction logic as needed
      paramNames.forEach(name => {
        if (name !== 'id' && name !== 'slug') {
          const index = segments.indexOf(name);
          if (index !== -1 && index + 1 < segments.length) {
            params[name] = segments[index + 1];
          }
        }
      });

      return schema.parse(params);
    } catch (error) {
      if (error instanceof ZodError) {
        throw new ValidationError('Parameter validation failed', error.errors);
      }
      throw error;
    }
  }

  /**
   * Validate request headers
   */
  static validateHeaders<T>(req: NextRequest, schema: ZodSchema<T>): T {
    try {
      const headers: Record<string, string> = {};
      req.headers.forEach((value, key) => {
        headers[key.toLowerCase()] = value;
      });

      return schema.parse(headers);
    } catch (error) {
      if (error instanceof ZodError) {
        throw new ValidationError('Header validation failed', error.errors);
      }
      throw error;
    }
  }

  /**
   * Transform query string values to appropriate types
   */
  private static transformQueryTypes(query: Record<string, string>): Record<string, any> {
    const transformed: Record<string, any> = {};

    Object.entries(query).forEach(([key, value]) => {
      // Boolean transformation
      if (value === 'true' || value === 'false') {
        transformed[key] = value === 'true';
        return;
      }

      // Number transformation
      if (/^\d+$/.test(value)) {
        transformed[key] = parseInt(value, 10);
        return;
      }

      if (/^\d+\.\d+$/.test(value)) {
        transformed[key] = parseFloat(value);
        return;
      }

      // Array transformation (comma-separated values)
      if (value.includes(',')) {
        transformed[key] = value.split(',').map(v => v.trim());
        return;
      }

      // Keep as string
      transformed[key] = value;
    });

    return transformed;
  }

  /**
   * Process FormData for multipart requests
   */
  private static processFormData(formData: FormData): Record<string, any> {
    const result: Record<string, any> = {};

    formData.forEach((value, key) => {
      if (value instanceof File) {
        result[key] = value;
      } else {
        // Try to parse JSON values
        try {
          result[key] = JSON.parse(value);
        } catch {
          result[key] = value;
        }
      }
    });

    return result;
  }
}

/**
 * Custom validation error class
 */
export class ValidationError extends Error {
  constructor(
    message: string,
    public errors: Array<{
      path: (string | number)[];
      message: string;
      code: string;
    }>
  ) {
    super(message);
    this.name = 'ValidationError';
  }

  /**
   * Get formatted error messages
   */
  getFormattedErrors(): Record<string, string[]> {
    const formatted: Record<string, string[]> = {};

    this.errors.forEach(error => {
      const path = error.path.join('.');
      if (!formatted[path]) {
        formatted[path] = [];
      }
      formatted[path].push(error.message);
    });

    return formatted;
  }

  /**
   * Get first error message for each field
   */
  getFieldErrors(): Record<string, string> {
    const fieldErrors: Record<string, string> = {};

    this.errors.forEach(error => {
      const path = error.path.join('.');
      if (!fieldErrors[path]) {
        fieldErrors[path] = error.message;
      }
    });

    return fieldErrors;
  }
}

/**
 * Common validation schemas
 */
export const CommonSchemas = {
  // Pagination
  pagination: z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  }),

  // UUID parameter
  uuidParam: z.object({
    id: z.string().uuid('Invalid ID format'),
  }),

  // Slug parameter
  slugParam: z.object({
    slug: z.string().min(1).max(255),
  }),

  // Search query
  searchQuery: z.object({
    q: z.string().min(1).max(255),
    ...CommonSchemas.pagination.shape,
  }),

  // Common headers for authenticated requests
  authHeaders: z.object({
    authorization: z.string().startsWith('Bearer '),
  }),

  // File upload
  fileUpload: z.object({
    file: z.instanceof(File),
    folder: z.string().optional(),
    alt: z.string().optional(),
    title: z.string().optional(),
  }),

  // Bulk operations
  bulkOperation: z.object({
    action: z.enum(['delete', 'update', 'activate', 'deactivate']),
    ids: z.array(z.string().uuid()).min(1).max(100),
    data: z.any().optional(),
  }),

  // Date range filter
  dateRange: z.object({
    startDate: z.string().datetime().optional(),
    endDate: z.string().datetime().optional(),
  }),

  // Common filters
  commonFilters: z.object({
    search: z.string().optional(),
    status: z.string().optional(),
    isActive: z.coerce.boolean().optional(),
    createdAt: z.object({
      gte: z.string().datetime().optional(),
      lte: z.string().datetime().optional(),
    }).optional(),
  }),
};

/**
 * Validation decorators for common patterns
 */
export const ValidationDecorators = {
  /**
   * Create a schema that validates email format
   */
  email: () => z.string().email('Invalid email format'),

  /**
   * Create a schema that validates password strength
   */
  password: (minLength = 8) => 
    z.string()
      .min(minLength, `Password must be at least ${minLength} characters`)
      .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
      .regex(/[0-9]/, 'Password must contain at least one number'),

  /**
   * Create a schema that validates phone number
   */
  phone: () => 
    z.string()
      .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format'),

  /**
   * Create a schema that validates URL
   */
  url: () => z.string().url('Invalid URL format'),

  /**
   * Create a schema that validates slug format
   */
  slug: () => 
    z.string()
      .regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens')
      .min(1)
      .max(255),

  /**
   * Create a schema that validates price
   */
  price: () => z.number().min(0, 'Price must be non-negative'),

  /**
   * Create a schema that validates percentage
   */
  percentage: () => z.number().min(0).max(100, 'Percentage must be between 0 and 100'),

  /**
   * Create a schema that validates color hex code
   */
  hexColor: () => 
    z.string()
      .regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color format'),

  /**
   * Create a schema that validates JSON string
   */
  jsonString: () => 
    z.string()
      .refine(val => {
        try {
          JSON.parse(val);
          return true;
        } catch {
          return false;
        }
      }, 'Invalid JSON format'),
};

/**
 * Sanitization helpers
 */
export const Sanitizers = {
  /**
   * Sanitize HTML content
   */
  html: (value: string): string => {
    // Basic HTML sanitization - in production, use a proper library like DOMPurify
    return value
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  },

  /**
   * Sanitize SQL input
   */
  sql: (value: string): string => {
    return value.replace(/['";\\]/g, '');
  },

  /**
   * Normalize whitespace
   */
  whitespace: (value: string): string => {
    return value.trim().replace(/\s+/g, ' ');
  },

  /**
   * Generate safe slug
   */
  slug: (value: string): string => {
    return value
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  },
};