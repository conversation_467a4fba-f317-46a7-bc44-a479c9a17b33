import { NextRequest, NextResponse } from 'next/server';
import { rateLimiters } from './rateLimiter';
import { ValidationError } from './validation';

/**
 * Comprehensive API middleware pipeline
 */
export class ApiMiddleware {
  private static instance: ApiMiddleware;
  private requestId = 0;

  static getInstance(): ApiMiddleware {
    if (!ApiMiddleware.instance) {
      ApiMiddleware.instance = new ApiMiddleware();
    }
    return ApiMiddleware.instance;
  }

  /**
   * Main middleware pipeline
   */
  async process(
    req: NextRequest,
    options: MiddlewareOptions = {}
  ): Promise<NextResponse | null> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // 1. Request logging
      if (options.logging !== false) {
        this.logRequest(req, requestId);
      }

      // 2. CORS handling
      if (options.cors !== false) {
        const corsResponse = this.handleCORS(req);
        if (corsResponse) return corsResponse;
      }

      // 3. Security headers
      if (options.security !== false) {
        this.addSecurityHeaders(req);
      }

      // 4. Rate limiting
      if (options.rateLimit) {
        const rateLimitResponse = await this.handleRateLimit(req, options.rateLimit);
        if (rateLimitResponse) return rateLimitResponse;
      }

      // 5. Authentication
      if (options.auth) {
        const authResponse = await this.handleAuthentication(req, options.auth);
        if (authResponse) return authResponse;
      }

      // 6. Authorization
      if (options.permissions) {
        const authzResponse = await this.handleAuthorization(req, options.permissions);
        if (authzResponse) return authzResponse;
      }

      // 7. Request size validation
      if (options.maxBodySize) {
        const sizeResponse = await this.validateRequestSize(req, options.maxBodySize);
        if (sizeResponse) return sizeResponse;
      }

      // 8. Content type validation
      if (options.allowedContentTypes) {
        const contentTypeResponse = this.validateContentType(req, options.allowedContentTypes);
        if (contentTypeResponse) return contentTypeResponse;
      }

      // Store middleware info for controllers
      (req as any).middleware = {
        requestId,
        startTime,
        user: (req as any).user,
        rateLimitInfo: (req as any).rateLimitInfo,
      };

      return null; // Continue to handler
    } catch (error) {
      return this.handleMiddlewareError(error, requestId);
    }
  }

  /**
   * Handle CORS preflight and headers
   */
  private handleCORS(req: NextRequest): NextResponse | null {
    const origin = req.headers.get('origin');
    const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': origin && allowedOrigins.includes(origin) ? origin : allowedOrigins[0],
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    return null;
  }

  /**
   * Add security headers to request
   */
  private addSecurityHeaders(req: NextRequest): void {
    // Store security headers to be added to response
    (req as any).securityHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    };
  }

  /**
   * Handle rate limiting
   */
  private async handleRateLimit(
    req: NextRequest,
    rateLimitType: string
  ): Promise<NextResponse | null> {
    const limiter = rateLimiters[rateLimitType as keyof typeof rateLimiters];
    
    if (!limiter) {
      console.warn(`Unknown rate limit type: ${rateLimitType}`);
      return null;
    }

    return await limiter.middleware()(req);
  }

  /**
   * Handle authentication
   */
  private async handleAuthentication(
    req: NextRequest,
    authConfig: AuthConfig
  ): Promise<NextResponse | null> {
    try {
      const token = this.extractToken(req);
      
      if (!token) {
        if (authConfig.required) {
          return this.createUnauthorizedResponse('Authentication token required');
        }
        return null;
      }

      // Verify token (simplified - implement proper JWT verification)
      const user = await this.verifyToken(token);
      
      if (!user) {
        return this.createUnauthorizedResponse('Invalid or expired token');
      }

      if (authConfig.requiredStatus && user.status !== authConfig.requiredStatus) {
        return this.createForbiddenResponse('Account status does not allow access');
      }

      // Store user in request
      (req as any).user = user;
      
      return null;
    } catch (error) {
      console.error('Authentication error:', error);
      return this.createUnauthorizedResponse('Authentication failed');
    }
  }

  /**
   * Handle authorization (permissions)
   */
  private async handleAuthorization(
    req: NextRequest,
    permissions: string[]
  ): Promise<NextResponse | null> {
    const user = (req as any).user;
    
    if (!user) {
      return this.createUnauthorizedResponse('Authentication required for authorization');
    }

    // Check if user has required permissions
    const hasPermission = permissions.every(permission => 
      user.permissions?.includes(permission)
    );

    if (!hasPermission) {
      return this.createForbiddenResponse('Insufficient permissions');
    }

    return null;
  }

  /**
   * Validate request size
   */
  private async validateRequestSize(
    req: NextRequest,
    maxSize: number
  ): Promise<NextResponse | null> {
    const contentLength = req.headers.get('content-length');
    
    if (contentLength && parseInt(contentLength) > maxSize) {
      return NextResponse.json(
        {
          success: false,
          message: 'Request body too large',
          error: 'PAYLOAD_TOO_LARGE',
          maxSize: maxSize,
        },
        { status: 413 }
      );
    }

    return null;
  }

  /**
   * Validate content type
   */
  private validateContentType(
    req: NextRequest,
    allowedTypes: string[]
  ): NextResponse | null {
    const contentType = req.headers.get('content-type') || '';
    const isAllowed = allowedTypes.some(type => contentType.includes(type));

    if (req.method !== 'GET' && req.method !== 'DELETE' && !isAllowed) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unsupported content type',
          error: 'UNSUPPORTED_MEDIA_TYPE',
          allowedTypes,
        },
        { status: 415 }
      );
    }

    return null;
  }

  /**
   * Extract authentication token from request
   */
  private extractToken(req: NextRequest): string | null {
    const authHeader = req.headers.get('authorization');
    
    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check for token in cookies
    const tokenCookie = req.cookies.get('auth-token');
    if (tokenCookie) {
      return tokenCookie.value;
    }

    return null;
  }

  /**
   * Verify authentication token
   */
  private async verifyToken(token: string): Promise<any | null> {
    try {
      // Implement proper JWT verification here
      // This is a simplified placeholder
      
      // In production, use a proper JWT library
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      
      // Check expiration
      if (payload.exp && payload.exp < Date.now() / 1000) {
        return null;
      }

      return {
        id: payload.sub,
        email: payload.email,
        role: payload.role,
        permissions: payload.permissions || [],
        status: payload.status,
      };
    } catch (error) {
      console.error('Token verification error:', error);
      return null;
    }
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${++this.requestId}`;
  }

  /**
   * Log incoming request
   */
  private logRequest(req: NextRequest, requestId: string): void {
    const url = new URL(req.url);
    console.log(`🌐 ${req.method} ${url.pathname} [${requestId}]`);
  }

  /**
   * Handle middleware errors
   */
  private handleMiddlewareError(error: any, requestId: string): NextResponse {
    console.error(`❌ Middleware error [${requestId}]:`, error);

    if (error instanceof ValidationError) {
      return NextResponse.json(
        {
          success: false,
          message: 'Validation failed',
          error: 'VALIDATION_ERROR',
          details: error.getFieldErrors(),
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message: 'Internal server error',
        error: 'INTERNAL_ERROR',
        requestId,
      },
      { status: 500 }
    );
  }

  /**
   * Create unauthorized response
   */
  private createUnauthorizedResponse(message: string): NextResponse {
    return NextResponse.json(
      {
        success: false,
        message,
        error: 'UNAUTHORIZED',
      },
      { status: 401 }
    );
  }

  /**
   * Create forbidden response
   */
  private createForbiddenResponse(message: string): NextResponse {
    return NextResponse.json(
      {
        success: false,
        message,
        error: 'FORBIDDEN',
      },
      { status: 403 }
    );
  }

  /**
   * Add security headers to response
   */
  static addSecurityHeadersToResponse(response: NextResponse, req: NextRequest): NextResponse {
    const securityHeaders = (req as any).securityHeaders;
    
    if (securityHeaders) {
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value as string);
      });
    }

    return response;
  }
}

interface MiddlewareOptions {
  logging?: boolean;
  cors?: boolean;
  security?: boolean;
  rateLimit?: string;
  auth?: AuthConfig;
  permissions?: string[];
  maxBodySize?: number;
  allowedContentTypes?: string[];
}

interface AuthConfig {
  required: boolean;
  requiredStatus?: string;
}

// Export singleton instance
export const apiMiddleware = ApiMiddleware.getInstance();

/**
 * Middleware factory for common configurations
 */
export const MiddlewarePresets = {
  // Public API endpoints
  public: {
    logging: true,
    cors: true,
    security: true,
    rateLimit: 'api',
  },

  // Protected API endpoints
  protected: {
    logging: true,
    cors: true,
    security: true,
    rateLimit: 'api',
    auth: { required: true },
  },

  // Admin endpoints
  admin: {
    logging: true,
    cors: true,
    security: true,
    rateLimit: 'admin',
    auth: { required: true, requiredStatus: 'active' },
    permissions: ['ADMIN_ACCESS'],
  },

  // File upload endpoints
  upload: {
    logging: true,
    cors: true,
    security: true,
    rateLimit: 'upload',
    auth: { required: true },
    maxBodySize: 50 * 1024 * 1024, // 50MB
    allowedContentTypes: ['multipart/form-data'],
  },

  // Authentication endpoints
  auth: {
    logging: true,
    cors: true,
    security: true,
    rateLimit: 'auth',
    allowedContentTypes: ['application/json'],
  },

  // Search endpoints
  search: {
    logging: true,
    cors: true,
    security: true,
    rateLimit: 'search',
  },
} as const;