import { NextRequest, NextResponse } from 'next/server';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: NextRequest) => string;
  onLimitReached?: (req: NextRequest, rateLimitInfo: RateLimitInfo) => void;
}

interface RateLimitInfo {
  totalHits: number;
  totalDuration: number;
  remainingRequests: number;
  resetTime: number;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  windowStart: number;
}

/**
 * Rate limiter middleware for API endpoints
 */
export class RateLimiter {
  private static instances = new Map<string, RateLimiter>();
  private store = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor(private config: RateLimitConfig) {
    // Cleanup expired entries every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60 * 1000);
  }

  static create(name: string, config: RateLimitConfig): RateLimiter {
    if (!RateLimiter.instances.has(name)) {
      RateLimiter.instances.set(name, new RateLimiter(config));
    }
    return RateLimiter.instances.get(name)!;
  }

  /**
   * Middleware function to check rate limits
   */
  middleware() {
    return async (req: NextRequest): Promise<NextResponse | null> => {
      const key = this.generateKey(req);
      const now = Date.now();
      
      // Get or create rate limit entry
      let entry = this.store.get(key);
      
      if (!entry || now > entry.resetTime) {
        // Create new window
        entry = {
          count: 0,
          resetTime: now + this.config.windowMs,
          windowStart: now,
        };
        this.store.set(key, entry);
      }

      // Check if limit exceeded
      if (entry.count >= this.config.maxRequests) {
        const rateLimitInfo: RateLimitInfo = {
          totalHits: entry.count,
          totalDuration: now - entry.windowStart,
          remainingRequests: 0,
          resetTime: entry.resetTime,
        };

        // Call onLimitReached callback
        this.config.onLimitReached?.(req, rateLimitInfo);

        // Return rate limit exceeded response
        return NextResponse.json(
          {
            success: false,
            message: this.config.message || 'Too many requests, please try again later',
            error: 'RATE_LIMIT_EXCEEDED',
            retryAfter: Math.ceil((entry.resetTime - now) / 1000),
          },
          { 
            status: 429,
            headers: {
              'X-RateLimit-Limit': this.config.maxRequests.toString(),
              'X-RateLimit-Remaining': '0',
              'X-RateLimit-Reset': Math.ceil(entry.resetTime / 1000).toString(),
              'Retry-After': Math.ceil((entry.resetTime - now) / 1000).toString(),
            },
          }
        );
      }

      // Increment counter
      entry.count++;
      this.store.set(key, entry);

      // Add rate limit headers to response (will be handled by the controller)
      const remainingRequests = Math.max(0, this.config.maxRequests - entry.count);
      
      // Store rate limit info in request for controllers to access
      (req as any).rateLimitInfo = {
        totalHits: entry.count,
        totalDuration: now - entry.windowStart,
        remainingRequests,
        resetTime: entry.resetTime,
      };

      return null; // Continue to next middleware/handler
    };
  }

  /**
   * Generate rate limit key based on request
   */
  private generateKey(req: NextRequest): string {
    if (this.config.keyGenerator) {
      return this.config.keyGenerator(req);
    }

    // Default: use IP address and route
    const ip = this.getClientIP(req);
    const route = new URL(req.url).pathname;
    return `${ip}:${route}`;
  }

  /**
   * Get client IP address from request
   */
  private getClientIP(req: NextRequest): string {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    if (realIP) {
      return realIP;
    }
    
    return req.ip || 'unknown';
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.store.forEach((entry, key) => {
      if (now > entry.resetTime) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => {
      this.store.delete(key);
    });

    if (keysToDelete.length > 0) {
      console.log(`🧹 Rate limiter cleaned up ${keysToDelete.length} expired entries`);
    }
  }

  /**
   * Get current rate limit status for a request
   */
  getStatus(req: NextRequest): RateLimitInfo | null {
    const key = this.generateKey(req);
    const entry = this.store.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();
    return {
      totalHits: entry.count,
      totalDuration: now - entry.windowStart,
      remainingRequests: Math.max(0, this.config.maxRequests - entry.count),
      resetTime: entry.resetTime,
    };
  }

  /**
   * Reset rate limit for a specific key
   */
  reset(req: NextRequest): void {
    const key = this.generateKey(req);
    this.store.delete(key);
  }

  /**
   * Get all rate limit statistics
   */
  getStats(): {
    totalEntries: number;
    activeWindows: number;
    topKeys: Array<{ key: string; count: number; resetTime: number }>;
  } {
    const now = Date.now();
    const activeEntries = Array.from(this.store.entries())
      .filter(([_, entry]) => now <= entry.resetTime);

    const topKeys = activeEntries
      .sort(([, a], [, b]) => b.count - a.count)
      .slice(0, 10)
      .map(([key, entry]) => ({
        key: key.split(':')[0] + ':***', // Mask full key for privacy
        count: entry.count,
        resetTime: entry.resetTime,
      }));

    return {
      totalEntries: this.store.size,
      activeWindows: activeEntries.length,
      topKeys,
    };
  }

  /**
   * Destroy rate limiter and clean up resources
   */
  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.store.clear();
  }
}

/**
 * Predefined rate limiter configurations
 */
export const RateLimitPresets = {
  // General API endpoints
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: 'Too many API requests, please try again later',
  },

  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    message: 'Too many authentication attempts, please try again later',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || req.ip || 'unknown';
      return `auth:${ip}`;
    },
  },

  // Search endpoints
  search: {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 30,
    message: 'Too many search requests, please slow down',
  },

  // Admin endpoints
  admin: {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 60,
    message: 'Admin rate limit exceeded',
    keyGenerator: (req: NextRequest) => {
      // Use user ID if available, otherwise IP
      const userId = req.headers.get('x-user-id');
      const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || req.ip || 'unknown';
      return `admin:${userId || ip}`;
    },
  },

  // File upload endpoints
  upload: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 10,
    message: 'Too many upload attempts, please wait before uploading again',
  },

  // Password reset endpoints
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many password reset attempts, please try again later',
    keyGenerator: (req: NextRequest) => {
      const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || req.ip || 'unknown';
      return `password-reset:${ip}`;
    },
  },

  // Cart operations
  cart: {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 20,
    message: 'Too many cart operations, please slow down',
  },

  // Order creation
  order: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5,
    message: 'Too many order attempts, please wait before trying again',
  },
} as const;

/**
 * Create rate limiter instances for common use cases
 */
export const rateLimiters = {
  api: RateLimiter.create('api', RateLimitPresets.api),
  auth: RateLimiter.create('auth', RateLimitPresets.auth),
  search: RateLimiter.create('search', RateLimitPresets.search),
  admin: RateLimiter.create('admin', RateLimitPresets.admin),
  upload: RateLimiter.create('upload', RateLimitPresets.upload),
  passwordReset: RateLimiter.create('passwordReset', RateLimitPresets.passwordReset),
  cart: RateLimiter.create('cart', RateLimitPresets.cart),
  order: RateLimiter.create('order', RateLimitPresets.order),
};

/**
 * Helper function to add rate limit headers to response
 */
export function addRateLimitHeaders(
  response: NextResponse,
  rateLimitInfo: RateLimitInfo
): NextResponse {
  response.headers.set('X-RateLimit-Limit', '100'); // This should come from config
  response.headers.set('X-RateLimit-Remaining', rateLimitInfo.remainingRequests.toString());
  response.headers.set('X-RateLimit-Reset', Math.ceil(rateLimitInfo.resetTime / 1000).toString());
  
  return response;
}