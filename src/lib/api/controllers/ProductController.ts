import { NextRequest } from 'next/server';
import { z } from 'zod';
import { BaseController } from './BaseController';
import { ProductService, productService } from '@/lib/services';
import { ProductStatus, AttributeType } from '@/types/core';

// Validation schemas
const productQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'price', 'createdAt', 'avgRating']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  categoryId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  minPrice: z.coerce.number().min(0).optional(),
  maxPrice: z.coerce.number().min(0).optional(),
  featured: z.coerce.boolean().optional(),
  status: z.nativeEnum(ProductStatus).optional(),
  tags: z.string().transform(val => val.split(',')).optional(),
});

const productCreateSchema = z.object({
  name: z.string().min(1).max(255),
  slug: z.string().min(1).max(255),
  description: z.string().optional(),
  shortDescription: z.string().max(500).optional(),
  price: z.number().min(0),
  salePrice: z.number().min(0).optional(),
  sku: z.string().min(1).max(100),
  stock: z.number().int().min(0),
  status: z.nativeEnum(ProductStatus).default(ProductStatus.ACTIVE),
  featured: z.boolean().default(false),
  weight: z.number().min(0).optional(),
  dimensions: z.string().optional(),
  tags: z.array(z.string()).default([]),
  metaTitle: z.string().max(255).optional(),
  metaDescription: z.string().max(500).optional(),
  categoryId: z.string().uuid().optional(),
  brandId: z.string().uuid().optional(),
  images: z.array(z.object({
    mediaId: z.string().uuid(),
    sortOrder: z.number().int().min(0),
    isMain: z.boolean().default(false),
  })).optional(),
  attributes: z.array(z.object({
    attributeId: z.string().uuid(),
    attributeValueId: z.string().uuid().optional(),
    customValue: z.string().optional(),
  })).optional(),
});

const productUpdateSchema = productCreateSchema.partial();

const slugSchema = z.object({
  slug: z.string().min(1),
});

export class ProductController extends BaseController<ProductService> {
  protected service = productService;
  protected entityName = 'Product';

  // GET /api/products
  async getProducts(req: NextRequest) {
    return this.handleList(req, {
      cache: { ttl: 300 }, // 5 minutes cache
    }, {
      query: productQuerySchema,
    });
  }

  // GET /api/products/[id]
  async getProduct(req: NextRequest) {
    return this.handleGetById(req, {
      cache: { ttl: 600 }, // 10 minutes cache
    });
  }

  // GET /api/products/slug/[slug]
  async getProductBySlug(req: NextRequest) {
    try {
      const { slug } = this.validateParams(req, slugSchema);
      
      const cacheKey = this.generateCacheKey('getBySlug', { slug });
      const cached = await this.getCachedResponse(cacheKey);
      if (cached) {
        return this.success(cached);
      }

      const result = await this.service.getProductBySlug(slug);
      
      if (!result) {
        return this.notFound('Product not found');
      }

      await this.setCachedResponse(cacheKey, result, 600);
      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET /api/products/featured
  async getFeaturedProducts(req: NextRequest) {
    try {
      const url = new URL(req.url);
      const limit = Number(url.searchParams.get('limit')) || 8;

      const cacheKey = this.generateCacheKey('featured', { limit });
      const cached = await this.getCachedResponse(cacheKey);
      if (cached) {
        return this.success(cached);
      }

      const result = await this.service.getFeaturedProducts(limit);
      
      await this.setCachedResponse(cacheKey, result, 900); // 15 minutes
      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET /api/products/trending
  async getTrendingProducts(req: NextRequest) {
    try {
      const url = new URL(req.url);
      const limit = Number(url.searchParams.get('limit')) || 8;

      const cacheKey = this.generateCacheKey('trending', { limit });
      const cached = await this.getCachedResponse(cacheKey);
      if (cached) {
        return this.success(cached);
      }

      const result = await this.service.getTrendingProducts(limit);
      
      await this.setCachedResponse(cacheKey, result, 600); // 10 minutes
      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET /api/products/related/[id]
  async getRelatedProducts(req: NextRequest) {
    try {
      const { id } = this.validateParams(req, this.defaultParamsSchema);
      const url = new URL(req.url);
      const limit = Number(url.searchParams.get('limit')) || 4;

      const cacheKey = this.generateCacheKey('related', { id, limit });
      const cached = await this.getCachedResponse(cacheKey);
      if (cached) {
        return this.success(cached);
      }

      const result = await this.service.getRelatedProducts(id, limit);
      
      await this.setCachedResponse(cacheKey, result, 1800); // 30 minutes
      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST /api/admin/products
  async createProduct(req: NextRequest) {
    return this.handleCreate(req, {
      requireAuth: true,
      requiredPermissions: ['PRODUCTS_CREATE'],
    }, {
      body: productCreateSchema,
    });
  }

  // PUT /api/admin/products/[id]
  async updateProduct(req: NextRequest) {
    return this.handleUpdate(req, {
      requireAuth: true,
      requiredPermissions: ['PRODUCTS_UPDATE'],
    }, {
      body: productUpdateSchema,
    });
  }

  // DELETE /api/admin/products/[id]
  async deleteProduct(req: NextRequest) {
    return this.handleDelete(req, {
      requireAuth: true,
      requiredPermissions: ['PRODUCTS_DELETE'],
    });
  }

  // POST /api/admin/products/bulk
  async bulkOperations(req: NextRequest) {
    try {
      const body = await req.json();
      const { action, productIds, data } = z.object({
        action: z.enum(['delete', 'updateStatus', 'updateCategory', 'updateBrand']),
        productIds: z.array(z.string().uuid()),
        data: z.any().optional(),
      }).parse(body);

      let result;
      switch (action) {
        case 'delete':
          result = await this.service.executeInTransaction(async (tx) => {
            for (const id of productIds) {
              await tx.product.delete({ where: { id } });
            }
            return { deleted: productIds.length };
          });
          break;

        case 'updateStatus':
          result = await this.service.updateMany({
            where: { id: { in: productIds } },
            data: { status: data.status },
          });
          break;

        case 'updateCategory':
          result = await this.service.updateMany({
            where: { id: { in: productIds } },
            data: { categoryId: data.categoryId },
          });
          break;

        case 'updateBrand':
          result = await this.service.updateMany({
            where: { id: { in: productIds } },
            data: { brandId: data.brandId },
          });
          break;

        default:
          return this.badRequest('Invalid bulk action');
      }

      // Invalidate all product caches
      await this.invalidateListCaches();
      for (const id of productIds) {
        await this.invalidateResourceCaches(id);
      }

      return this.success(result, `Bulk ${action} completed successfully`);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // GET /api/admin/products/stats
  async getProductStats(req: NextRequest) {
    try {
      const cacheKey = this.generateCacheKey('stats', {});
      const cached = await this.getCachedResponse(cacheKey);
      if (cached) {
        return this.success(cached);
      }

      const [total, active, outOfStock, lowStock] = await Promise.all([
        this.service.count(),
        this.service.count({ where: { status: ProductStatus.ACTIVE } }),
        this.service.count({ where: { status: ProductStatus.OUT_OF_STOCK } }),
        this.service.count({ where: { stock: { lte: 10 } } }),
      ]);

      const stats = {
        total,
        active,
        outOfStock,
        lowStock,
      };

      await this.setCachedResponse(cacheKey, stats, 900); // 15 minutes
      return this.success(stats);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // POST /api/products/search
  async searchProducts(req: NextRequest) {
    try {
      const body = await req.json();
      const { query, filters, page, limit } = z.object({
        query: z.string().min(1),
        filters: z.object({
          categoryId: z.string().uuid().optional(),
          brandId: z.string().uuid().optional(),
          minPrice: z.number().min(0).optional(),
          maxPrice: z.number().min(0).optional(),
          tags: z.array(z.string()).optional(),
        }).optional(),
        page: z.number().min(1).default(1),
        limit: z.number().min(1).max(50).default(20),
      }).parse(body);

      const cacheKey = this.generateCacheKey('search', { query, filters, page, limit });
      const cached = await this.getCachedResponse(cacheKey);
      if (cached) {
        return this.success(cached);
      }

      const result = await this.service.searchProducts(query, filters, page, limit);
      
      await this.setCachedResponse(cacheKey, result, 300); // 5 minutes
      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Utility methods for product-specific operations
  protected async invalidateProductCaches(productId?: string) {
    // Invalidate specific patterns for products
    const patterns = [
      'product:list:*',
      'product:featured:*',
      'product:trending:*',
      'product:search:*',
      'product:stats:*',
    ];

    if (productId) {
      patterns.push(
        `product:get:*${productId}*`,
        `product:related:*${productId}*`
      );
    }

    for (const pattern of patterns) {
      // Implement cache invalidation for pattern
    }
  }
}

// Export singleton instance
export const productController = new ProductController();