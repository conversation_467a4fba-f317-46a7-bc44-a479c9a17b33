import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema, z } from 'zod';
import { ApiResponse, PaginatedResponse, BaseListParams } from '@/types/core';
import { BaseService } from '@/lib/services/base';

export interface ControllerOptions {
  requireAuth?: boolean;
  requiredPermissions?: string[];
  rateLimit?: {
    requests: number;
    windowMs: number;
  };
  cache?: {
    ttl: number;
    key?: (req: NextRequest) => string;
  };
}

export interface ValidationSchemas {
  query?: ZodSchema;
  body?: ZodSchema;
  params?: ZodSchema;
}

export abstract class BaseController<TService extends BaseService<any, any, any>> {
  protected abstract service: TService;
  protected abstract entityName: string;

  // Default validation schemas
  protected defaultQuerySchema = z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
    search: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  });

  protected defaultParamsSchema = z.object({
    id: z.string().uuid(),
  });

  /**
   * Generic GET handler for listing resources
   */
  async handleList(
    req: NextRequest,
    options: ControllerOptions = {},
    schemas: ValidationSchemas = {}
  ): Promise<NextResponse> {
    try {
      // Validate query parameters
      const querySchema = schemas.query || this.defaultQuerySchema;
      const query = this.validateQuery(req, querySchema);

      // Check cache first
      if (options.cache) {
        const cacheKey = options.cache.key?.(req) || this.generateCacheKey('list', query);
        const cached = await this.getCachedResponse(cacheKey);
        if (cached) {
          return this.success(cached);
        }
      }

      // Fetch data from service
      const result = await this.service.findManyPaginated(query);
      
      // Cache the result
      if (options.cache) {
        const cacheKey = options.cache.key?.(req) || this.generateCacheKey('list', query);
        await this.setCachedResponse(cacheKey, result, options.cache.ttl);
      }

      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Generic GET handler for single resource
   */
  async handleGetById(
    req: NextRequest,
    options: ControllerOptions = {},
    schemas: ValidationSchemas = {}
  ): Promise<NextResponse> {
    try {
      // Validate params
      const paramsSchema = schemas.params || this.defaultParamsSchema;
      const { id } = this.validateParams(req, paramsSchema);

      // Check cache first
      if (options.cache) {
        const cacheKey = options.cache.key?.(req) || this.generateCacheKey('get', { id });
        const cached = await this.getCachedResponse(cacheKey);
        if (cached) {
          return this.success(cached);
        }
      }

      // Fetch data from service
      const result = await this.service.findUnique({ where: { id } });
      
      if (!result) {
        return this.notFound(`${this.entityName} not found`);
      }

      // Cache the result
      if (options.cache) {
        const cacheKey = options.cache.key?.(req) || this.generateCacheKey('get', { id });
        await this.setCachedResponse(cacheKey, result, options.cache.ttl);
      }

      return this.success(result);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Generic POST handler for creating resources
   */
  async handleCreate(
    req: NextRequest,
    options: ControllerOptions = {},
    schemas: ValidationSchemas = {}
  ): Promise<NextResponse> {
    try {
      // Validate request body
      if (!schemas.body) {
        throw new Error('Body schema is required for create operations');
      }

      const body = await this.validateBody(req, schemas.body);

      // Create resource
      const result = await this.service.create({ data: body });

      // Invalidate related caches
      await this.invalidateListCaches();

      return this.created(result, `${this.entityName} created successfully`);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Generic PUT handler for updating resources
   */
  async handleUpdate(
    req: NextRequest,
    options: ControllerOptions = {},
    schemas: ValidationSchemas = {}
  ): Promise<NextResponse> {
    try {
      // Validate params and body
      const paramsSchema = schemas.params || this.defaultParamsSchema;
      const { id } = this.validateParams(req, paramsSchema);

      if (!schemas.body) {
        throw new Error('Body schema is required for update operations');
      }

      const body = await this.validateBody(req, schemas.body);

      // Update resource
      const result = await this.service.update({ where: { id }, data: body });

      // Invalidate caches
      await this.invalidateResourceCaches(id);

      return this.success(result, `${this.entityName} updated successfully`);
    } catch (error) {
      return this.handleError(error);
    }
  }

  /**
   * Generic DELETE handler for removing resources
   */
  async handleDelete(
    req: NextRequest,
    options: ControllerOptions = {},
    schemas: ValidationSchemas = {}
  ): Promise<NextResponse> {
    try {
      // Validate params
      const paramsSchema = schemas.params || this.defaultParamsSchema;
      const { id } = this.validateParams(req, paramsSchema);

      // Delete resource
      await this.service.delete({ where: { id } });

      // Invalidate caches
      await this.invalidateResourceCaches(id);

      return this.success(null, `${this.entityName} deleted successfully`);
    } catch (error) {
      return this.handleError(error);
    }
  }

  // Validation helpers
  protected validateQuery(req: NextRequest, schema: ZodSchema): any {
    const url = new URL(req.url);
    const query = Object.fromEntries(url.searchParams.entries());
    return schema.parse(query);
  }

  protected validateParams(req: NextRequest, schema: ZodSchema): any {
    // Extract params from URL pathname
    const pathname = new URL(req.url).pathname;
    const segments = pathname.split('/').filter(Boolean);
    
    // Simple param extraction - can be enhanced based on routing needs
    const params: Record<string, string> = {};
    if (segments.length > 0) {
      params.id = segments[segments.length - 1];
    }
    
    return schema.parse(params);
  }

  protected async validateBody(req: NextRequest, schema: ZodSchema): Promise<any> {
    try {
      const body = await req.json();
      return schema.parse(body);
    } catch (error) {
      throw new Error('Invalid JSON body');
    }
  }

  // Response helpers
  protected success<T>(data: T, message?: string): NextResponse<ApiResponse<T>> {
    return NextResponse.json({
      success: true,
      data,
      message: message || 'Success',
    });
  }

  protected created<T>(data: T, message?: string): NextResponse<ApiResponse<T>> {
    return NextResponse.json({
      success: true,
      data,
      message: message || 'Created successfully',
    }, { status: 201 });
  }

  protected notFound(message?: string): NextResponse<ApiResponse<null>> {
    return NextResponse.json({
      success: false,
      data: null,
      message: message || 'Resource not found',
    }, { status: 404 });
  }

  protected badRequest(message?: string, errors?: any): NextResponse<ApiResponse<null>> {
    return NextResponse.json({
      success: false,
      data: null,
      message: message || 'Bad request',
      error: errors,
    }, { status: 400 });
  }

  protected unauthorized(message?: string): NextResponse<ApiResponse<null>> {
    return NextResponse.json({
      success: false,
      data: null,
      message: message || 'Unauthorized',
    }, { status: 401 });
  }

  protected forbidden(message?: string): NextResponse<ApiResponse<null>> {
    return NextResponse.json({
      success: false,
      data: null,
      message: message || 'Forbidden',
    }, { status: 403 });
  }

  protected internalError(message?: string, error?: any): NextResponse<ApiResponse<null>> {
    console.error('Controller error:', error);
    return NextResponse.json({
      success: false,
      data: null,
      message: message || 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error?.message : undefined,
    }, { status: 500 });
  }

  // Error handling
  protected handleError(error: any): NextResponse {
    if (error instanceof z.ZodError) {
      return this.badRequest('Validation error', error.errors);
    }

    if (error.code === 'P2002') {
      return this.badRequest('Unique constraint violation');
    }

    if (error.code === 'P2025') {
      return this.notFound(`${this.entityName} not found`);
    }

    return this.internalError('An unexpected error occurred', error);
  }

  // Cache helpers
  protected generateCacheKey(operation: string, params: any): string {
    const key = `${this.entityName.toLowerCase()}:${operation}:${JSON.stringify(params)}`;
    return key.replace(/[^a-zA-Z0-9:_-]/g, '_');
  }

  protected async getCachedResponse(key: string): Promise<any | null> {
    // Implement cache retrieval logic
    // This would typically use Redis or similar
    return null;
  }

  protected async setCachedResponse(key: string, data: any, ttl: number): Promise<void> {
    // Implement cache storage logic
    // This would typically use Redis or similar
  }

  protected async invalidateListCaches(): Promise<void> {
    // Implement cache invalidation for list operations
    // This would invalidate patterns like "entity:list:*"
  }

  protected async invalidateResourceCaches(id: string): Promise<void> {
    // Implement cache invalidation for specific resource
    // This would invalidate patterns like "entity:get:*" and "entity:list:*"
  }

  // Utility methods
  protected extractUserFromRequest(req: NextRequest): any {
    // Extract user information from JWT token or session
    // Implementation depends on your auth strategy
    return null;
  }

  protected checkPermissions(user: any, requiredPermissions: string[]): boolean {
    // Check if user has required permissions
    // Implementation depends on your permission system
    return true;
  }

  protected applyRateLimit(req: NextRequest, limit: { requests: number; windowMs: number }): boolean {
    // Apply rate limiting logic
    // This would typically use Redis or in-memory store
    return true;
  }
}