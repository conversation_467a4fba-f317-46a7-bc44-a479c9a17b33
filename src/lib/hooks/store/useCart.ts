import { useCallback } from 'react';
import { useCartStore, cartSelectors, type CartStore } from '@/lib/stores/CartStore';
import { useShallow } from 'zustand/react/shallow';

/**
 * Custom hook for cart functionality with optimized selectors
 */
export function useCart() {
  // Use shallow comparison for object selections
  const cart = useCartStore(useShallow(cartSelectors.cart));
  const isOpen = useCartStore(cartSelectors.isOpen);
  const loading = useCartStore(cartSelectors.loading);
  const error = useCartStore(cartSelectors.error);
  const summary = useCartStore(useShallow(cartSelectors.summary));
  const isEmpty = useCartStore(cartSelectors.isEmpty);
  const hasItems = useCartStore(cartSelectors.hasItems);
  const itemCount = useCartStore(cartSelectors.itemCount);
  const totalQuantity = useCartStore(cartSelectors.totalQuantity);
  
  // Actions
  const fetchCart = useCartStore(state => state.fetchCart);
  const openCart = useCartStore(state => state.openCart);
  const closeCart = useCartStore(state => state.closeCart);
  const toggleCart = useCartStore(state => state.toggleCart);
  const addToCart = useCartStore(state => state.addToCart);
  const updateCartItem = useCartStore(state => state.updateCartItem);
  const removeFromCart = useCartStore(state => state.removeFromCart);
  const clearCart = useCartStore(state => state.clearCart);
  const getItemQuantity = useCartStore(state => state.getItemQuantity);
  const isInCart = useCartStore(state => state.isInCart);
  const canAddToCart = useCartStore(state => state.canAddToCart);
  const quickAdd = useCartStore(state => state.quickAdd);
  
  return {
    // State
    cart,
    isOpen,
    loading,
    error,
    summary,
    isEmpty,
    hasItems,
    itemCount,
    totalQuantity,
    
    // Actions
    fetchCart,
    openCart,
    closeCart,
    toggleCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    getItemQuantity,
    isInCart,
    canAddToCart,
    quickAdd,
  };
}

/**
 * Hook for cart item operations with optimistic updates
 */
export function useCartItem(productId: string) {
  const quantity = useCartStore(state => state.getItemQuantity(productId));
  const isInCart = useCartStore(state => state.isInCart(productId));
  const canAdd = useCartStore(state => state.canAddToCart(productId, 1));
  
  const addToCart = useCartStore(state => state.addToCart);
  const updateQuantity = useCartStore(state => state.updateCartItem);
  const removeFromCart = useCartStore(state => state.removeFromCart);
  
  const increment = useCallback(async () => {
    if (isInCart) {
      const item = useCartStore.getState().cart?.items.find(item => item.productId === productId);
      if (item) {
        await updateQuantity(item.id, quantity + 1);
      }
    } else {
      await addToCart(productId, 1);
    }
  }, [productId, isInCart, quantity, addToCart, updateQuantity]);
  
  const decrement = useCallback(async () => {
    if (quantity > 1) {
      const item = useCartStore.getState().cart?.items.find(item => item.productId === productId);
      if (item) {
        await updateQuantity(item.id, quantity - 1);
      }
    } else if (quantity === 1) {
      const item = useCartStore.getState().cart?.items.find(item => item.productId === productId);
      if (item) {
        await removeFromCart(item.id);
      }
    }
  }, [productId, quantity, updateQuantity, removeFromCart]);
  
  const setQuantity = useCallback(async (newQuantity: number) => {
    if (newQuantity <= 0) {
      const item = useCartStore.getState().cart?.items.find(item => item.productId === productId);
      if (item) {
        await removeFromCart(item.id);
      }
    } else {
      const item = useCartStore.getState().cart?.items.find(item => item.productId === productId);
      if (item) {
        await updateQuantity(item.id, newQuantity);
      } else {
        await addToCart(productId, newQuantity);
      }
    }
  }, [productId, addToCart, updateQuantity, removeFromCart]);
  
  const remove = useCallback(async () => {
    const item = useCartStore.getState().cart?.items.find(item => item.productId === productId);
    if (item) {
      await removeFromCart(item.id);
    }
  }, [productId, removeFromCart]);
  
  return {
    quantity,
    isInCart,
    canAdd,
    increment,
    decrement,
    setQuantity,
    remove,
  };
}

/**
 * Hook for cart summary and calculations
 */
export function useCartSummary() {
  const summary = useCartStore(useShallow(cartSelectors.summary));
  const cart = useCartStore(useShallow(cartSelectors.cart));
  const calculateSummary = useCartStore(state => state.calculateSummary);
  
  const getItemTotal = useCallback((productId: string) => {
    if (!cart) return 0;
    const item = cart.items.find(item => item.productId === productId);
    if (!item) return 0;
    const price = item.product.salePrice || item.product.price;
    return price * item.quantity;
  }, [cart]);
  
  const getTaxAmount = useCallback(() => summary?.tax || 0, [summary]);
  const getShippingAmount = useCallback(() => summary?.shipping || 0, [summary]);
  const getSubtotal = useCallback(() => summary?.subtotal || 0, [summary]);
  const getTotal = useCallback(() => summary?.total || 0, [summary]);
  
  return {
    summary,
    calculateSummary,
    getItemTotal,
    getTaxAmount,
    getShippingAmount,
    getSubtotal,
    getTotal,
  };
}

/**
 * Hook for cart validation and error handling
 */
export function useCartValidation() {
  const cart = useCartStore(useShallow(cartSelectors.cart));
  const error = useCartStore(cartSelectors.error);
  const validateCart = useCartStore(state => state.validateCart);
  
  const hasOutOfStockItems = useCallback(() => {
    if (!cart) return false;
    return cart.items.some(item => {
      const product = item.product as any;
      return product.stock !== undefined && product.stock < item.quantity;
    });
  }, [cart]);
  
  const getOutOfStockItems = useCallback(() => {
    if (!cart) return [];
    return cart.items.filter(item => {
      const product = item.product as any;
      return product.stock !== undefined && product.stock < item.quantity;
    });
  }, [cart]);
  
  const hasUnavailableItems = useCallback(() => {
    if (!cart) return false;
    return cart.items.some(item => {
      const product = item.product as any;
      return product.status !== 'ACTIVE';
    });
  }, [cart]);
  
  const isValid = useCallback(() => {
    return !hasOutOfStockItems() && !hasUnavailableItems() && !error;
  }, [hasOutOfStockItems, hasUnavailableItems, error]);
  
  return {
    error,
    validateCart,
    hasOutOfStockItems,
    getOutOfStockItems,
    hasUnavailableItems,
    isValid,
  };
}

/**
 * Hook for bulk cart operations
 */
export function useBulkCartOperations() {
  const addMultipleItems = useCartStore(state => state.addMultipleItems);
  const updateMultipleItems = useCartStore(state => state.updateMultipleItems);
  const loading = useCartStore(cartSelectors.loading);
  
  const addWishlistToCart = useCallback(async (wishlistItems: Array<{ productId: string; quantity?: number }>) => {
    const items = wishlistItems.map(item => ({
      productId: item.productId,
      quantity: item.quantity || 1,
    }));
    await addMultipleItems(items);
  }, [addMultipleItems]);
  
  const updateAllQuantities = useCallback(async (updates: Array<{ itemId: string; quantity: number }>) => {
    await updateMultipleItems(updates);
  }, [updateMultipleItems]);
  
  return {
    loading,
    addMultipleItems,
    updateMultipleItems,
    addWishlistToCart,
    updateAllQuantities,
  };
}

/**
 * Hook for cart persistence and sync
 */
export function useCartSync() {
  const syncWithServer = useCartStore(state => state.syncWithServer);
  const loading = useCartStore(cartSelectors.loading);
  const lastFetch = useCartStore(state => state.lastFetch);
  
  const needsSync = useCallback(() => {
    if (!lastFetch) return true;
    const fiveMinutes = 5 * 60 * 1000;
    return Date.now() - lastFetch > fiveMinutes;
  }, [lastFetch]);
  
  const autoSync = useCallback(async () => {
    if (needsSync()) {
      await syncWithServer();
    }
  }, [needsSync, syncWithServer]);
  
  return {
    loading,
    syncWithServer,
    needsSync,
    autoSync,
  };
}

/**
 * Hook for cart analytics and tracking
 */
export function useCartAnalytics() {
  const cart = useCartStore(useShallow(cartSelectors.cart));
  const summary = useCartStore(useShallow(cartSelectors.summary));
  
  const trackAddToCart = useCallback((productId: string, quantity: number) => {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'add_to_cart', {
        currency: 'VND',
        value: summary?.subtotal || 0,
        items: [{
          item_id: productId,
          quantity: quantity,
        }],
      });
    }
  }, [summary]);
  
  const trackRemoveFromCart = useCallback((productId: string) => {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'remove_from_cart', {
        currency: 'VND',
        value: summary?.subtotal || 0,
        items: [{
          item_id: productId,
        }],
      });
    }
  }, [summary]);
  
  const trackViewCart = useCallback(() => {
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'view_cart', {
        currency: 'VND',
        value: summary?.subtotal || 0,
        items: cart?.items.map(item => ({
          item_id: item.productId,
          item_name: item.product.name,
          quantity: item.quantity,
          price: item.product.salePrice || item.product.price,
        })) || [],
      });
    }
  }, [cart, summary]);
  
  const getCartMetrics = useCallback(() => {
    if (!cart || !summary) return null;
    
    return {
      itemCount: cart.items.length,
      totalQuantity: summary.totalQuantity,
      subtotal: summary.subtotal,
      averageItemValue: cart.items.length > 0 ? summary.subtotal / cart.items.length : 0,
      categories: [...new Set(cart.items.map(item => (item.product as any).categoryId))],
      brands: [...new Set(cart.items.map(item => (item.product as any).brandId))],
    };
  }, [cart, summary]);
  
  return {
    trackAddToCart,
    trackRemoveFromCart,
    trackViewCart,
    getCartMetrics,
  };
}