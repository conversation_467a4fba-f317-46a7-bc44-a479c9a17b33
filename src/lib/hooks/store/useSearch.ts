import { useCallback, useEffect, useMemo } from 'react';
import { useSearchStore, searchSelectors, type SearchStore, type SearchFilters } from '@/lib/stores/SearchStore';
import { useShallow } from 'zustand/react/shallow';
import { useDebouncedCallback } from 'use-debounce';

/**
 * Main search hook with debounced search functionality
 */
export function useSearch() {
  const query = useSearchStore(searchSelectors.query);
  const results = useSearchStore(useShallow(searchSelectors.results));
  const loading = useSearchStore(searchSelectors.loading);
  const error = useSearchStore(searchSelectors.error);
  const hasSearched = useSearchStore(searchSelectors.hasSearched);
  const hasResults = useSearchStore(searchSelectors.hasResults);
  const totalResults = useSearchStore(searchSelectors.totalResults);
  
  // Actions
  const setQuery = useSearchStore(state => state.setQuery);
  const search = useSearchStore(state => state.search);
  const clearSearch = useSearchStore(state => state.clearSearch);
  
  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebouncedCallback(
    (searchQuery: string) => {
      if (searchQuery.trim()) {
        search(searchQuery);
      }
    },
    300
  );
  
  const handleQueryChange = useCallback((newQuery: string) => {
    setQuery(newQuery);
    debouncedSearch(newQuery);
  }, [setQuery, debouncedSearch]);
  
  const searchWithOptions = useCallback(async (
    searchQuery?: string, 
    options?: { page?: number; limit?: number }
  ) => {
    await search(searchQuery, options);
  }, [search]);
  
  return {
    // State
    query,
    results,
    loading,
    error,
    hasSearched,
    hasResults,
    totalResults,
    
    // Actions
    setQuery,
    search: searchWithOptions,
    clearSearch,
    handleQueryChange,
  };
}

/**
 * Hook for search suggestions with debounced requests
 */
export function useSearchSuggestions(delayMs: number = 200) {
  const query = useSearchStore(searchSelectors.query);
  const suggestions = useSearchStore(useShallow(searchSelectors.suggestions));
  const suggestionsLoading = useSearchStore(searchSelectors.suggestionsLoading);
  
  const getSuggestions = useSearchStore(state => state.getSuggestions);
  const clearSuggestions = useSearchStore(state => state.clearSuggestions);
  
  // Debounced suggestions fetching
  const debouncedGetSuggestions = useDebouncedCallback(
    (searchQuery: string) => {
      if (searchQuery.trim().length >= 2) {
        getSuggestions(searchQuery);
      } else {
        clearSuggestions();
      }
    },
    delayMs
  );
  
  // Auto-fetch suggestions when query changes
  useEffect(() => {
    debouncedGetSuggestions(query);
  }, [query, debouncedGetSuggestions]);
  
  const selectSuggestion = useCallback((suggestion: string) => {
    useSearchStore.getState().setQuery(suggestion);
    useSearchStore.getState().search(suggestion);
    clearSuggestions();
  }, [clearSuggestions]);
  
  return {
    suggestions,
    loading: suggestionsLoading,
    getSuggestions,
    clearSuggestions,
    selectSuggestion,
  };
}

/**
 * Hook for search filters management
 */
export function useSearchFilters() {
  const filters = useSearchStore(useShallow(searchSelectors.filters));
  const results = useSearchStore(useShallow(searchSelectors.results));
  const advancedMode = useSearchStore(searchSelectors.advancedMode);
  
  const setFilters = useSearchStore(state => state.setFilters);
  const clearFilters = useSearchStore(state => state.clearFilters);
  const advancedSearch = useSearchStore(state => state.advancedSearch);
  const toggleAdvancedMode = useSearchStore(state => state.toggleAdvancedMode);
  const saveFilters = useSearchStore(state => state.saveFilters);
  const loadSavedFilters = useSearchStore(state => state.loadSavedFilters);
  
  // Available filters from current search results
  const availableFilters = results?.filters.availableFilters;
  
  const hasActiveFilters = useMemo(() => {
    return Object.keys(filters).some(key => {
      const value = filters[key as keyof SearchFilters];
      return value !== undefined && value !== null && 
             (Array.isArray(value) ? value.length > 0 : true);
    });
  }, [filters]);
  
  const getActiveFilterCount = useCallback(() => {
    return Object.keys(filters).filter(key => {
      const value = filters[key as keyof SearchFilters];
      return value !== undefined && value !== null && 
             (Array.isArray(value) ? value.length > 0 : true);
    }).length;
  }, [filters]);
  
  const updateFilter = useCallback(<K extends keyof SearchFilters>(
    key: K, 
    value: SearchFilters[K]
  ) => {
    setFilters({ [key]: value });
  }, [setFilters]);
  
  const removeFilter = useCallback((key: keyof SearchFilters) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    setFilters(newFilters);
  }, [filters, setFilters]);
  
  const toggleFilter = useCallback(<K extends keyof SearchFilters>(
    key: K,
    value: NonNullable<SearchFilters[K]> extends (infer U)[] ? U : SearchFilters[K]
  ) => {
    const currentValue = filters[key];
    
    if (Array.isArray(currentValue)) {
      const valueArray = currentValue as any[];
      const newValue = valueArray.includes(value)
        ? valueArray.filter(v => v !== value)
        : [...valueArray, value];
      setFilters({ [key]: newValue.length > 0 ? newValue : undefined });
    } else {
      setFilters({ [key]: currentValue === value ? undefined : value });
    }
  }, [filters, setFilters]);
  
  const applyFilters = useCallback(async () => {
    const query = useSearchStore.getState().query;
    await advancedSearch(filters, query);
  }, [filters, advancedSearch]);
  
  return {
    // State
    filters,
    availableFilters,
    advancedMode,
    hasActiveFilters,
    
    // Actions
    setFilters,
    clearFilters,
    updateFilter,
    removeFilter,
    toggleFilter,
    applyFilters,
    toggleAdvancedMode,
    saveFilters,
    loadSavedFilters,
    getActiveFilterCount,
  };
}

/**
 * Hook for search history management
 */
export function useSearchHistory() {
  const searchHistory = useSearchStore(useShallow(searchSelectors.searchHistory));
  
  const addToHistory = useSearchStore(state => state.addToHistory);
  const removeFromHistory = useSearchStore(state => state.removeFromHistory);
  const clearHistory = useSearchStore(state => state.clearHistory);
  
  const hasHistory = searchHistory.length > 0;
  
  const searchFromHistory = useCallback((query: string) => {
    useSearchStore.getState().setQuery(query);
    useSearchStore.getState().search(query);
  }, []);
  
  const getRecentSearches = useCallback((limit: number = 5) => {
    return searchHistory.slice(0, limit);
  }, [searchHistory]);
  
  return {
    searchHistory,
    hasHistory,
    addToHistory,
    removeFromHistory,
    clearHistory,
    searchFromHistory,
    getRecentSearches,
  };
}

/**
 * Hook for popular searches
 */
export function usePopularSearches() {
  const popularSearches = useSearchStore(useShallow(searchSelectors.popularSearches));
  const loading = useSearchStore(state => state.popularLoading);
  
  const fetchPopularSearches = useSearchStore(state => state.fetchPopularSearches);
  
  const searchPopular = useCallback((query: string) => {
    useSearchStore.getState().setQuery(query);
    useSearchStore.getState().search(query);
  }, []);
  
  const getTopSearches = useCallback((limit: number = 10) => {
    return popularSearches.slice(0, limit);
  }, [popularSearches]);
  
  return {
    popularSearches,
    loading,
    fetchPopularSearches,
    searchPopular,
    getTopSearches,
  };
}

/**
 * Hook for search pagination
 */
export function useSearchPagination() {
  const results = useSearchStore(useShallow(searchSelectors.results));
  const loading = useSearchStore(searchSelectors.loading);
  
  const loadMore = useSearchStore(state => state.loadMore);
  const goToPage = useSearchStore(state => state.goToPage);
  
  const currentPage = results?.page || 1;
  const totalPages = results ? Math.ceil(results.total / results.limit) : 0;
  const hasNextPage = useSearchStore(searchSelectors.hasNextPage);
  const hasPrevPage = currentPage > 1;
  
  const canLoadMore = hasNextPage && !loading;
  
  const goToNextPage = useCallback(async () => {
    if (hasNextPage) {
      await goToPage(currentPage + 1);
    }
  }, [hasNextPage, currentPage, goToPage]);
  
  const goToPrevPage = useCallback(async () => {
    if (hasPrevPage) {
      await goToPage(currentPage - 1);
    }
  }, [hasPrevPage, currentPage, goToPage]);
  
  const goToFirstPage = useCallback(async () => {
    if (currentPage > 1) {
      await goToPage(1);
    }
  }, [currentPage, goToPage]);
  
  const goToLastPage = useCallback(async () => {
    if (currentPage < totalPages) {
      await goToPage(totalPages);
    }
  }, [currentPage, totalPages, goToPage]);
  
  return {
    currentPage,
    totalPages,
    hasNextPage,
    hasPrevPage,
    canLoadMore,
    loading,
    loadMore,
    goToPage,
    goToNextPage,
    goToPrevPage,
    goToFirstPage,
    goToLastPage,
  };
}

/**
 * Hook for search analytics and metrics
 */
export function useSearchAnalytics() {
  const searchMetrics = useSearchStore(useShallow(searchSelectors.searchMetrics));
  const trackSearch = useSearchStore(state => state.trackSearch);
  const getSearchAnalytics = useSearchStore(state => state.getSearchAnalytics);
  
  const averageSearchTime = searchMetrics.searchDuration;
  const totalSearches = searchMetrics.totalSearches;
  const lastSearchTime = searchMetrics.lastSearchTime;
  
  const getAnalyticsSummary = useCallback(() => {
    const analytics = getSearchAnalytics();
    
    if (!analytics.length) return null;
    
    const totalQueries = analytics.length;
    const uniqueQueries = new Set(analytics.map((a: any) => a.query)).size;
    const averageResults = analytics.reduce((sum: number, a: any) => sum + a.resultCount, 0) / totalQueries;
    const zeroResultQueries = analytics.filter((a: any) => a.resultCount === 0).length;
    
    const popularQueries = analytics
      .reduce((acc: any, curr: any) => {
        acc[curr.query] = (acc[curr.query] || 0) + 1;
        return acc;
      }, {});
    
    const topQueries = Object.entries(popularQueries)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 10)
      .map(([query, count]) => ({ query, count }));
    
    return {
      totalQueries,
      uniqueQueries,
      averageResults,
      zeroResultQueries,
      zeroResultRate: (zeroResultQueries / totalQueries) * 100,
      topQueries,
    };
  }, [getSearchAnalytics]);
  
  const trackSearchEvent = useCallback((query: string, resultCount: number, metadata?: any) => {
    trackSearch(query, resultCount);
    
    // Additional custom tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'search', {
        search_term: query,
        result_count: resultCount,
        ...metadata,
      });
    }
  }, [trackSearch]);
  
  return {
    searchMetrics,
    averageSearchTime,
    totalSearches,
    lastSearchTime,
    getAnalyticsSummary,
    trackSearchEvent,
  };
}

/**
 * Hook for search export functionality
 */
export function useSearchExport() {
  const results = useSearchStore(useShallow(searchSelectors.results));
  const exportResults = useSearchStore(state => state.exportResults);
  const loading = useSearchStore(searchSelectors.loading);
  
  const canExport = !!results && results.total > 0;
  
  const exportAsCSV = useCallback(async () => {
    if (canExport) {
      await exportResults('csv');
    }
  }, [canExport, exportResults]);
  
  const exportAsJSON = useCallback(async () => {
    if (canExport) {
      await exportResults('json');
    }
  }, [canExport, exportResults]);
  
  return {
    canExport,
    loading,
    exportAsCSV,
    exportAsJSON,
  };
}

/**
 * Hook for search URL management and deep linking
 */
export function useSearchUrl() {
  const query = useSearchStore(searchSelectors.query);
  const filters = useSearchStore(useShallow(searchSelectors.filters));
  const getSearchUrl = useSearchStore(state => state.getSearchUrl);
  
  const currentSearchUrl = useMemo(() => {
    return getSearchUrl(query, filters);
  }, [query, filters, getSearchUrl]);
  
  const createShareableUrl = useCallback((baseUrl?: string) => {
    const url = baseUrl || window.location.origin;
    return `${url}${currentSearchUrl}`;
  }, [currentSearchUrl]);
  
  const copySearchUrl = useCallback(async () => {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(createShareableUrl());
    }
  }, [createShareableUrl]);
  
  return {
    currentSearchUrl,
    createShareableUrl,
    copySearchUrl,
  };
}

/**
 * Hook for real-time search with WebSocket (if implemented)
 */
export function useRealTimeSearch() {
  const query = useSearchStore(searchSelectors.query);
  const results = useSearchStore(useShallow(searchSelectors.results));
  
  // This would connect to a WebSocket for real-time search updates
  useEffect(() => {
    if (!query) return;
    
    // Placeholder for WebSocket connection
    // const ws = new WebSocket(`ws://localhost:3001/search?q=${encodeURIComponent(query)}`);
    // 
    // ws.onmessage = (event) => {
    //   const data = JSON.parse(event.data);
    //   // Update search results in real-time
    // };
    // 
    // return () => ws.close();
  }, [query]);
  
  return {
    isConnected: false, // Would track WebSocket connection status
    lastUpdate: null,   // Would track last real-time update
  };
}