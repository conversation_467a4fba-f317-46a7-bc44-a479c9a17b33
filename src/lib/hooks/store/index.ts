/**
 * Centralized export for all store-based hooks
 * This file provides a single import point for all state management hooks
 */

// Cart hooks
export {
  useCart,
  useCartItem,
  useCartSummary,
  useCartValidation,
  useBulkCartOperations,
  useCartSync,
  useCartAnalytics,
} from './useCart';

// User hooks
export {
  useUser,
  useUserProfile,
  useUserAddresses,
  useUserWishlist,
  useUserPreferences,
  useUserSecurity,
  useUserSession,
  useUserDataManagement,
  useUserNotifications,
} from './useUser';

// Search hooks
export {
  useSearch,
  useSearchSuggestions,
  useSearchFilters,
  useSearchHistory,
  usePopularSearches,
  useSearchPagination,
  useSearchAnalytics,
  useSearchExport,
  useSearchUrl,
  useRealTimeSearch,
} from './useSearch';

// Store instances (for direct access when needed)
export { useCartStore, cartSelectors } from '@/lib/stores/CartStore';
export { useUserStore, userSelectors } from '@/lib/stores/UserStore';
export { useSearchStore, searchSelectors } from '@/lib/stores/SearchStore';

// Types
export type { CartStore } from '@/lib/stores/CartStore';
export type { UserStore } from '@/lib/stores/UserStore';
export type { SearchStore, SearchFilters } from '@/lib/stores/SearchStore';