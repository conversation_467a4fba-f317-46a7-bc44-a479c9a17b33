import { useCallback, useEffect } from 'react';
import { useUserStore, userSelectors, type UserStore } from '@/lib/stores/UserStore';
import { useShallow } from 'zustand/react/shallow';
import { useSession } from 'next-auth/react';

/**
 * Main user hook with authentication integration
 */
export function useUser() {
  const { data: session, status } = useSession();
  
  // User state
  const user = useUserStore(useShallow(userSelectors.user));
  const isAuthenticated = useUserStore(userSelectors.isAuthenticated);
  const loading = useUserStore(userSelectors.loading);
  const error = useUserStore(userSelectors.error);
  const profileMetrics = useUserStore(useShallow(userSelectors.profileMetrics));
  
  // User actions
  const setUser = useUserStore(state => state.setUser);
  const fetchUser = useUserStore(state => state.fetchUser);
  const refreshUser = useUserStore(state => state.refreshUser);
  const logout = useUserStore(state => state.logout);
  const updateProfile = useUserStore(state => state.updateProfile);
  const uploadAvatar = useUserStore(state => state.uploadAvatar);
  const changePassword = useUserStore(state => state.changePassword);
  
  // Sync with NextAuth session
  useEffect(() => {
    if (status === 'authenticated' && session?.user && !user) {
      setUser(session.user as any);
      fetchUser();
    } else if (status === 'unauthenticated' && user) {
      setUser(null);
    }
  }, [session, status, user, setUser, fetchUser]);
  
  return {
    // State
    user,
    isAuthenticated,
    loading: loading || status === 'loading',
    error,
    profileMetrics,
    
    // Actions
    fetchUser,
    refreshUser,
    logout,
    updateProfile,
    uploadAvatar,
    changePassword,
  };
}

/**
 * Hook for user profile management
 */
export function useUserProfile() {
  const user = useUserStore(useShallow(userSelectors.user));
  const profileMetrics = useUserStore(useShallow(userSelectors.profileMetrics));
  const loading = useUserStore(userSelectors.loading);
  const error = useUserStore(userSelectors.error);
  
  const updateProfile = useUserStore(state => state.updateProfile);
  const uploadAvatar = useUserStore(state => state.uploadAvatar);
  const calculateProfileCompletion = useUserStore(state => state.calculateProfileCompletion);
  
  const isProfileComplete = profileMetrics.isComplete;
  const completionPercentage = profileMetrics.completionPercentage;
  const missingFields = profileMetrics.missingFields;
  
  const getNextFieldToComplete = useCallback(() => {
    return missingFields[0] || null;
  }, [missingFields]);
  
  const canCompleteProfile = useCallback(() => {
    return user && missingFields.length > 0;
  }, [user, missingFields]);
  
  return {
    user,
    loading,
    error,
    isProfileComplete,
    completionPercentage,
    missingFields,
    updateProfile,
    uploadAvatar,
    calculateProfileCompletion,
    getNextFieldToComplete,
    canCompleteProfile,
  };
}

/**
 * Hook for user addresses management
 */
export function useUserAddresses() {
  const addresses = useUserStore(useShallow(userSelectors.addresses));
  const loading = useUserStore(state => state.addressesLoading);
  const error = useUserStore(state => state.addressesError);
  const defaultAddress = useUserStore(useShallow(userSelectors.defaultAddress));
  
  const fetchAddresses = useUserStore(state => state.fetchAddresses);
  const addAddress = useUserStore(state => state.addAddress);
  const updateAddress = useUserStore(state => state.updateAddress);
  const deleteAddress = useUserStore(state => state.deleteAddress);
  const setDefaultAddress = useUserStore(state => state.setDefaultAddress);
  const getDefaultAddress = useUserStore(state => state.getDefaultAddress);
  
  const hasAddresses = addresses.length > 0;
  const hasDefaultAddress = !!defaultAddress;
  
  const getAddressById = useCallback((id: string) => {
    return addresses.find(addr => addr.id === id) || null;
  }, [addresses]);
  
  const canSetAsDefault = useCallback((id: string) => {
    const address = getAddressById(id);
    return address && !address.isDefault;
  }, [getAddressById]);
  
  return {
    addresses,
    loading,
    error,
    defaultAddress,
    hasAddresses,
    hasDefaultAddress,
    fetchAddresses,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    getDefaultAddress,
    getAddressById,
    canSetAsDefault,
  };
}

/**
 * Hook for user wishlist management
 */
export function useUserWishlist() {
  const wishlistItems = useUserStore(useShallow(userSelectors.wishlistItems));
  const loading = useUserStore(state => state.wishlistLoading);
  const error = useUserStore(state => state.wishlistError);
  
  const fetchWishlist = useUserStore(state => state.fetchWishlist);
  const addToWishlist = useUserStore(state => state.addToWishlist);
  const removeFromWishlist = useUserStore(state => state.removeFromWishlist);
  const clearWishlist = useUserStore(state => state.clearWishlist);
  const isInWishlist = useUserStore(state => state.isInWishlist);
  
  const wishlistCount = wishlistItems.length;
  const hasWishlistItems = wishlistCount > 0;
  
  const toggleWishlist = useCallback(async (productId: string) => {
    if (isInWishlist(productId)) {
      await removeFromWishlist(productId);
    } else {
      await addToWishlist(productId);
    }
  }, [isInWishlist, addToWishlist, removeFromWishlist]);
  
  return {
    wishlistItems,
    loading,
    error,
    wishlistCount,
    hasWishlistItems,
    fetchWishlist,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist,
    toggleWishlist,
  };
}

/**
 * Hook for user preferences management
 */
export function useUserPreferences() {
  const preferences = useUserStore(useShallow(userSelectors.preferences));
  const loading = useUserStore(state => state.preferencesLoading);
  
  const updatePreferences = useUserStore(state => state.updatePreferences);
  const resetPreferences = useUserStore(state => state.resetPreferences);
  
  const getPreference = useCallback((key: string, defaultValue?: any) => {
    if (!preferences) return defaultValue;
    const keys = key.split('.');
    let value = preferences as any;
    for (const k of keys) {
      value = value?.[k];
      if (value === undefined) return defaultValue;
    }
    return value;
  }, [preferences]);
  
  const setPreference = useCallback(async (key: string, value: any) => {
    if (!preferences) return;
    
    const keys = key.split('.');
    const newPreferences = { ...preferences };
    let current = newPreferences as any;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!current[k] || typeof current[k] !== 'object') {
        current[k] = {};
      }
      current = current[k];
    }
    
    current[keys[keys.length - 1]] = value;
    await updatePreferences(newPreferences);
  }, [preferences, updatePreferences]);
  
  const togglePreference = useCallback(async (key: string) => {
    const currentValue = getPreference(key, false);
    await setPreference(key, !currentValue);
  }, [getPreference, setPreference]);
  
  return {
    preferences,
    loading,
    updatePreferences,
    resetPreferences,
    getPreference,
    setPreference,
    togglePreference,
  };
}

/**
 * Hook for user security management
 */
export function useUserSecurity() {
  const user = useUserStore(useShallow(userSelectors.user));
  const loading = useUserStore(userSelectors.loading);
  const error = useUserStore(userSelectors.error);
  
  const changePassword = useUserStore(state => state.changePassword);
  const enable2FA = useUserStore(state => state.enable2FA);
  const disable2FA = useUserStore(state => state.disable2FA);
  const updateSecuritySettings = useUserStore(state => state.updateSecuritySettings);
  const verifyEmail = useUserStore(state => state.verifyEmail);
  
  const has2FA = user?.twoFactorEnabled || false;
  const isEmailVerified = !!user?.emailVerified;
  const lastPasswordChange = user?.lastPasswordChange;
  
  const needsPasswordChange = useCallback(() => {
    if (!lastPasswordChange) return true;
    const threeMonths = 3 * 30 * 24 * 60 * 60 * 1000;
    return Date.now() - new Date(lastPasswordChange).getTime() > threeMonths;
  }, [lastPasswordChange]);
  
  const getSecurityScore = useCallback(() => {
    let score = 0;
    if (isEmailVerified) score += 25;
    if (has2FA) score += 25;
    if (!needsPasswordChange()) score += 25;
    if (user?.phone) score += 25;
    return score;
  }, [isEmailVerified, has2FA, needsPasswordChange, user?.phone]);
  
  return {
    user,
    loading,
    error,
    has2FA,
    isEmailVerified,
    lastPasswordChange,
    changePassword,
    enable2FA,
    disable2FA,
    updateSecuritySettings,
    verifyEmail,
    needsPasswordChange,
    getSecurityScore,
  };
}

/**
 * Hook for user session management
 */
export function useUserSession() {
  const sessionData = useUserStore(useShallow(userSelectors.sessionData));
  const updateLastActivity = useUserStore(state => state.updateLastActivity);
  const extendSession = useUserStore(state => state.extendSession);
  
  const isSessionActive = !!sessionData;
  const lastActivity = sessionData?.lastActivity;
  const loginTime = sessionData?.loginTime;
  
  const getSessionDuration = useCallback(() => {
    if (!loginTime) return 0;
    return Date.now() - loginTime;
  }, [loginTime]);
  
  const getTimeSinceLastActivity = useCallback(() => {
    if (!lastActivity) return 0;
    return Date.now() - lastActivity;
  }, [lastActivity]);
  
  const isSessionExpiringSoon = useCallback(() => {
    const timeSinceActivity = getTimeSinceLastActivity();
    const thirtyMinutes = 30 * 60 * 1000;
    return timeSinceActivity > thirtyMinutes;
  }, [getTimeSinceLastActivity]);
  
  // Auto-update activity on component mount and user interaction
  useEffect(() => {
    updateLastActivity();
    
    const handleActivity = () => updateLastActivity();
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });
    
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [updateLastActivity]);
  
  return {
    sessionData,
    isSessionActive,
    lastActivity,
    loginTime,
    updateLastActivity,
    extendSession,
    getSessionDuration,
    getTimeSinceLastActivity,
    isSessionExpiringSoon,
  };
}

/**
 * Hook for user data management and privacy
 */
export function useUserDataManagement() {
  const user = useUserStore(useShallow(userSelectors.user));
  const loading = useUserStore(userSelectors.loading);
  const error = useUserStore(userSelectors.error);
  
  const requestDataExport = useUserStore(state => state.requestDataExport);
  const deleteAccount = useUserStore(state => state.deleteAccount);
  
  const canDeleteAccount = useCallback(() => {
    // Add any business logic for account deletion eligibility
    return !!user;
  }, [user]);
  
  const getDataSummary = useCallback(() => {
    if (!user) return null;
    
    return {
      profileData: !!user.name || !!user.email || !!user.phone,
      addressData: user.addresses?.length > 0,
      orderHistory: true, // Would need to check actual order data
      preferences: true,
      wishlist: true,
    };
  }, [user]);
  
  return {
    user,
    loading,
    error,
    requestDataExport,
    deleteAccount,
    canDeleteAccount,
    getDataSummary,
  };
}

/**
 * Hook for user notifications and communications
 */
export function useUserNotifications() {
  const preferences = useUserStore(useShallow(userSelectors.preferences));
  const updatePreferences = useUserStore(state => state.updatePreferences);
  
  const emailNotifications = preferences?.notifications.email || false;
  const smsNotifications = preferences?.notifications.sms || false;
  const pushNotifications = preferences?.notifications.push || false;
  const marketingNotifications = preferences?.notifications.marketing || false;
  
  const toggleEmailNotifications = useCallback(async () => {
    if (!preferences) return;
    await updatePreferences({
      ...preferences,
      notifications: {
        ...preferences.notifications,
        email: !emailNotifications,
      },
    });
  }, [preferences, emailNotifications, updatePreferences]);
  
  const toggleSmsNotifications = useCallback(async () => {
    if (!preferences) return;
    await updatePreferences({
      ...preferences,
      notifications: {
        ...preferences.notifications,
        sms: !smsNotifications,
      },
    });
  }, [preferences, smsNotifications, updatePreferences]);
  
  const togglePushNotifications = useCallback(async () => {
    if (!preferences) return;
    await updatePreferences({
      ...preferences,
      notifications: {
        ...preferences.notifications,
        push: !pushNotifications,
      },
    });
  }, [preferences, pushNotifications, updatePreferences]);
  
  const toggleMarketingNotifications = useCallback(async () => {
    if (!preferences) return;
    await updatePreferences({
      ...preferences,
      notifications: {
        ...preferences.notifications,
        marketing: !marketingNotifications,
      },
    });
  }, [preferences, marketingNotifications, updatePreferences]);
  
  return {
    emailNotifications,
    smsNotifications,
    pushNotifications,
    marketingNotifications,
    toggleEmailNotifications,
    toggleSmsNotifications,
    togglePushNotifications,
    toggleMarketingNotifications,
  };
}