/**
 * Testing utilities for Zustand stores
 */

import { act, renderHook } from '@testing-library/react';
import { ReactNode } from 'react';
import { create, StoreApi, UseBoundStore } from 'zustand';

/**
 * Create a test wrapper for store testing
 */
export function createStoreTestWrapper<T>(
  useStore: UseBoundStore<StoreApi<T>>,
  initialState?: Partial<T>
) {
  return function StoreTestWrapper({ children }: { children: ReactNode }) {
    // Reset store to initial state if provided
    if (initialState) {
      act(() => {
        useStore.setState(initialState as T, true);
      });
    }

    return <>{children}</>;
  };
}

/**
 * Test store state changes
 */
export function testStoreStateChange<T>(
  useStore: UseBoundStore<StoreApi<T>>,
  action: () => void,
  expectedStateChange: Partial<T>
) {
  const { result } = renderHook(() => useStore());
  
  act(() => {
    action();
  });

  // Check if expected state changes occurred
  Object.entries(expectedStateChange).forEach(([key, expectedValue]) => {
    expect((result.current as any)[key]).toEqual(expectedValue);
  });
}

/**
 * Mock store for testing
 */
export function createMockStore<T>(initialState: T): UseBoundStore<StoreApi<T>> {
  return create<T>(() => initialState);
}

/**
 * Store testing helpers
 */
export class StoreTestHelpers {
  /**
   * Test async actions with loading states
   */
  static async testAsyncAction<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    action: () => Promise<void>,
    loadingKey: keyof T = 'loading' as keyof T
  ) {
    const { result } = renderHook(() => useStore());

    // Execute async action
    await act(async () => {
      await action();
    });

    // Verify loading was set to true during action and false after
    const finalState = result.current;
    expect((finalState as any)[loadingKey]).toBe(false);
  }

  /**
   * Test error handling in stores
   */
  static async testErrorHandling<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    action: () => Promise<void>,
    expectedError: string,
    errorKey: keyof T = 'error' as keyof T
  ) {
    const { result } = renderHook(() => useStore());

    await act(async () => {
      try {
        await action();
      } catch {
        // Error is expected
      }
    });

    expect((result.current as any)[errorKey]).toBe(expectedError);
  }

  /**
   * Test optimistic updates
   */
  static testOptimisticUpdate<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    optimisticAction: () => void,
    expectedOptimisticState: Partial<T>,
    revertAction: () => void,
    expectedRevertedState: Partial<T>
  ) {
    const { result } = renderHook(() => useStore());

    // Test optimistic update
    act(() => {
      optimisticAction();
    });

    Object.entries(expectedOptimisticState).forEach(([key, expectedValue]) => {
      expect((result.current as any)[key]).toEqual(expectedValue);
    });

    // Test revert
    act(() => {
      revertAction();
    });

    Object.entries(expectedRevertedState).forEach(([key, expectedValue]) => {
      expect((result.current as any)[key]).toEqual(expectedValue);
    });
  }

  /**
   * Test store persistence
   */
  static testStorePersistence<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    setState: (state: Partial<T>) => void,
    testState: Partial<T>,
    storageKey: string
  ) {
    // Set state
    act(() => {
      setState(testState);
    });

    // Check if state was persisted to localStorage
    const persistedData = localStorage.getItem(storageKey);
    expect(persistedData).toBeTruthy();

    if (persistedData) {
      const parsed = JSON.parse(persistedData);
      Object.entries(testState).forEach(([key, expectedValue]) => {
        expect(parsed.state[key]).toEqual(expectedValue);
      });
    }
  }

  /**
   * Test store selectors
   */
  static testSelectors<T, R>(
    useStore: UseBoundStore<StoreApi<T>>,
    selector: (state: T) => R,
    expectedResult: R
  ) {
    const { result } = renderHook(() => useStore(selector));
    expect(result.current).toEqual(expectedResult);
  }

  /**
   * Test store subscriptions
   */
  static testSubscription<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    stateChange: () => void,
    callback: jest.Mock
  ) {
    // Subscribe to store changes
    const unsubscribe = useStore.subscribe(callback);

    act(() => {
      stateChange();
    });

    expect(callback).toHaveBeenCalled();

    // Cleanup
    unsubscribe();
  }
}

/**
 * Performance testing utilities for stores
 */
export class StorePerformanceTestUtils {
  /**
   * Measure store action performance
   */
  static async measureActionPerformance<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    action: () => Promise<void> | void,
    iterations: number = 100
  ): Promise<{
    averageTime: number;
    minTime: number;
    maxTime: number;
    totalTime: number;
  }> {
    const times: number[] = [];

    for (let i = 0; i < iterations; i++) {
      const startTime = performance.now();
      
      if (action.constructor.name === 'AsyncFunction') {
        await act(async () => {
          await (action as () => Promise<void>)();
        });
      } else {
        act(() => {
          (action as () => void)();
        });
      }
      
      const endTime = performance.now();
      times.push(endTime - startTime);
    }

    const totalTime = times.reduce((sum, time) => sum + time, 0);
    const averageTime = totalTime / iterations;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    return {
      averageTime,
      minTime,
      maxTime,
      totalTime,
    };
  }

  /**
   * Test memory usage of store operations
   */
  static measureMemoryUsage<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    operation: () => void,
    iterations: number = 1000
  ): {
    memoryBefore: number;
    memoryAfter: number;
    memoryDifference: number;
  } {
    // Force garbage collection if available
    if ('gc' in window) {
      (window as any).gc();
    }

    const memoryBefore = (performance as any).memory?.usedJSHeapSize || 0;

    for (let i = 0; i < iterations; i++) {
      act(() => {
        operation();
      });
    }

    const memoryAfter = (performance as any).memory?.usedJSHeapSize || 0;
    const memoryDifference = memoryAfter - memoryBefore;

    return {
      memoryBefore,
      memoryAfter,
      memoryDifference,
    };
  }

  /**
   * Test render performance with store changes
   */
  static measureRenderPerformance<T, R>(
    useStore: UseBoundStore<StoreApi<T>>,
    selector: (state: T) => R,
    stateChanges: Array<() => void>,
    iterations: number = 100
  ): {
    averageRenderTime: number;
    totalRenders: number;
  } {
    let renderCount = 0;
    const renderTimes: number[] = [];

    const { result } = renderHook(() => {
      const startTime = performance.now();
      const value = useStore(selector);
      const endTime = performance.now();
      
      renderTimes.push(endTime - startTime);
      renderCount++;
      
      return value;
    });

    // Execute state changes
    stateChanges.forEach(change => {
      for (let i = 0; i < iterations; i++) {
        act(() => {
          change();
        });
      }
    });

    const totalRenderTime = renderTimes.reduce((sum, time) => sum + time, 0);
    const averageRenderTime = totalRenderTime / renderCount;

    return {
      averageRenderTime,
      totalRenders: renderCount,
    };
  }
}

/**
 * Mock API responses for store testing
 */
export class MockApiService {
  private static responses = new Map<string, any>();
  private static delays = new Map<string, number>();

  /**
   * Set mock response for API endpoint
   */
  static setMockResponse(endpoint: string, response: any, delay: number = 0) {
    this.responses.set(endpoint, response);
    this.delays.set(endpoint, delay);
  }

  /**
   * Clear all mock responses
   */
  static clearMocks() {
    this.responses.clear();
    this.delays.clear();
  }

  /**
   * Mock fetch implementation
   */
  static async mockFetch(endpoint: string): Promise<Response> {
    const response = this.responses.get(endpoint);
    const delay = this.delays.get(endpoint) || 0;

    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    if (!response) {
      return new Response(JSON.stringify({ error: 'Not found' }), {
        status: 404,
        statusText: 'Not Found',
      });
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }
}

/**
 * Store test scenarios
 */
export const StoreTestScenarios = {
  /**
   * Test basic CRUD operations
   */
  testCrudOperations: function<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    actions: {
      create: (data: any) => Promise<void>;
      read: () => Promise<void>;
      update: (id: string, data: any) => Promise<void>;
      delete: (id: string) => Promise<void>;
    },
    testData: {
      createData: any;
      updateData: any;
      itemId: string;
    }
  ) {
    describe('CRUD Operations', () => {
      test('should create item', async () => {
        await StoreTestHelpers.testAsyncAction(
          useStore,
          () => actions.create(testData.createData)
        );
      });

      test('should read items', async () => {
        await StoreTestHelpers.testAsyncAction(
          useStore,
          () => actions.read()
        );
      });

      test('should update item', async () => {
        await StoreTestHelpers.testAsyncAction(
          useStore,
          () => actions.update(testData.itemId, testData.updateData)
        );
      });

      test('should delete item', async () => {
        await StoreTestHelpers.testAsyncAction(
          useStore,
          () => actions.delete(testData.itemId)
        );
      });
    });
  },

  /**
   * Test error scenarios
   */
  testErrorScenarios: function<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    errorActions: Array<{
      name: string;
      action: () => Promise<void>;
      expectedError: string;
    }>
  ) {
    describe('Error Handling', () => {
      errorActions.forEach(({ name, action, expectedError }) => {
        test(`should handle ${name} error`, async () => {
          await StoreTestHelpers.testErrorHandling(
            useStore,
            action,
            expectedError
          );
        });
      });
    });
  },

  /**
   * Test performance requirements
   */
  testPerformanceRequirements: function<T>(
    useStore: UseBoundStore<StoreApi<T>>,
    performanceTests: Array<{
      name: string;
      action: () => Promise<void> | void;
      maxTime: number; // in milliseconds
      iterations?: number;
    }>
  ) {
    describe('Performance Requirements', () => {
      performanceTests.forEach(({ name, action, maxTime, iterations = 100 }) => {
        test(`${name} should complete within ${maxTime}ms`, async () => {
          const performance = await StorePerformanceTestUtils.measureActionPerformance(
            useStore,
            action,
            iterations
          );

          expect(performance.averageTime).toBeLessThan(maxTime);
        });
      });
    });
  },
};

/**
 * Integration test helpers
 */
export const IntegrationTestHelpers = {
  /**
   * Test multiple stores working together
   */
  testStoreIntegration: async function<T1, T2>(
    store1: UseBoundStore<StoreApi<T1>>,
    store2: UseBoundStore<StoreApi<T2>>,
    integrationAction: () => Promise<void>,
    expectedStore1Changes: Partial<T1>,
    expectedStore2Changes: Partial<T2>
  ) {
    const { result: result1 } = renderHook(() => store1());
    const { result: result2 } = renderHook(() => store2());

    await act(async () => {
      await integrationAction();
    });

    Object.entries(expectedStore1Changes).forEach(([key, expectedValue]) => {
      expect((result1.current as any)[key]).toEqual(expectedValue);
    });

    Object.entries(expectedStore2Changes).forEach(([key, expectedValue]) => {
      expect((result2.current as any)[key]).toEqual(expectedValue);
    });
  },
};

const storeTestingUtils = {
  StoreTestHelpers,
  StorePerformanceTestUtils,
  MockApiService,
  StoreTestScenarios,
  IntegrationTestHelpers,
  createStoreTestWrapper,
  testStoreStateChange,
  createMockStore,
};

export default storeTestingUtils;