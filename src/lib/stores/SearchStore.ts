import { create } from 'zustand';
import { BaseStore, createBaseStore, withAsyncAction, shouldRefetch } from './base/BaseStore';
import { Product, Category } from '@/types/core/entities';

/**
 * Search filters interface
 */
export interface SearchFilters {
  categoryId?: string;
  brandId?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  featured?: boolean;
  onSale?: boolean;
  sortBy?: 'relevance' | 'price' | 'name' | 'rating' | 'newest';
  sortOrder?: 'asc' | 'desc';
  attributes?: Record<string, string[]>;
  tags?: string[];
}

/**
 * Search results interface
 */
export interface SearchResults {
  products: Product[];
  categories: Category[];
  total: number;
  page: number;
  limit: number;
  hasNextPage: boolean;
  filters: {
    availableFilters: {
      categories: Array<{ id: string; name: string; count: number }>;
      brands: Array<{ id: string; name: string; count: number }>;
      priceRange: { min: number; max: number };
      attributes: Record<string, Array<{ value: string; count: number }>>;
    };
    appliedFilters: SearchFilters;
  };
}

/**
 * Search suggestion interface
 */
export interface SearchSuggestion {
  query: string;
  type: 'product' | 'category' | 'brand' | 'history';
  count?: number;
  metadata?: any;
}

/**
 * Search store state interface
 */
interface SearchState extends BaseStore {
  // Current search
  query: string;
  results: SearchResults | null;
  hasSearched: boolean;
  
  // Search history
  searchHistory: string[];
  
  // Suggestions
  suggestions: SearchSuggestion[];
  suggestionsLoading: boolean;
  
  // Popular searches
  popularSearches: string[];
  popularLoading: boolean;
  
  // Filters
  filters: SearchFilters;
  savedFilters: Record<string, SearchFilters>;
  
  // Advanced search
  advancedMode: boolean;
  
  // Performance
  searchMetrics: {
    lastSearchTime: number;
    searchDuration: number;
    totalSearches: number;
  };
  
  // Cache
  searchCache: Map<string, { results: SearchResults; timestamp: number }>;
  lastSearchFetch?: number;
}

/**
 * Search store actions interface
 */
interface SearchActions {
  // Basic search
  setQuery: (query: string) => void;
  search: (query?: string, options?: { page?: number; limit?: number }) => Promise<void>;
  clearSearch: () => void;
  
  // Advanced search
  advancedSearch: (filters: SearchFilters, query?: string) => Promise<void>;
  toggleAdvancedMode: () => void;
  
  // Filters
  setFilters: (filters: Partial<SearchFilters>) => void;
  clearFilters: () => void;
  saveFilters: (name: string, filters: SearchFilters) => void;
  loadSavedFilters: (name: string) => void;
  deleteSavedFilters: (name: string) => void;
  
  // Suggestions
  getSuggestions: (query: string) => Promise<void>;
  clearSuggestions: () => void;
  
  // History
  addToHistory: (query: string) => void;
  removeFromHistory: (query: string) => void;
  clearHistory: () => void;
  
  // Popular searches
  fetchPopularSearches: () => Promise<void>;
  
  // Pagination
  loadMore: () => Promise<void>;
  goToPage: (page: number) => Promise<void>;
  
  // Utilities
  getSearchUrl: (query?: string, filters?: SearchFilters) => string;
  exportResults: (format: 'csv' | 'json') => Promise<void>;
  
  // Analytics
  trackSearch: (query: string, resultCount: number) => void;
  getSearchAnalytics: () => any;
}

/**
 * Combined search store interface
 */
export interface SearchStore extends SearchState, SearchActions {}

/**
 * Initial search state
 */
const initialSearchState: Omit<SearchState, keyof BaseStore> = {
  query: '',
  results: null,
  hasSearched: false,
  searchHistory: [],
  suggestions: [],
  suggestionsLoading: false,
  popularSearches: [],
  popularLoading: false,
  filters: {},
  savedFilters: {},
  advancedMode: false,
  searchMetrics: {
    lastSearchTime: 0,
    searchDuration: 0,
    totalSearches: 0,
  },
  searchCache: new Map(),
};

/**
 * Generate cache key for search
 */
function generateCacheKey(query: string, filters: SearchFilters, page: number = 1, limit: number = 20): string {
  const filterStr = JSON.stringify(filters);
  return `${query}:${filterStr}:${page}:${limit}`;
}

/**
 * Create search store
 */
export const useSearchStore = create<SearchStore>(
  createBaseStore<SearchStore>(
    'SearchStore',
    initialSearchState,
    (set, get) => ({
      // Basic search
      setQuery: (query: string) => {
        set((state) => {
          state.query = query;
        });
      },
      
      search: withAsyncAction<SearchStore>('SearchStore', 'search')(async function(
        query?: string, 
        options: { page?: number; limit?: number } = {}
      ) {
        const searchQuery = query || get().query;
        const { page = 1, limit = 20 } = options;
        const filters = get().filters;
        
        if (!searchQuery.trim()) {
          set((state) => {
            state.results = null;
            state.hasSearched = false;
          });
          return;
        }
        
        // Check cache first
        const cacheKey = generateCacheKey(searchQuery, filters, page, limit);
        const cached = get().searchCache.get(cacheKey);
        
        if (cached && Date.now() - cached.timestamp < 5 * 60 * 1000) {
          set((state) => {
            state.query = searchQuery;
            state.results = cached.results;
            state.hasSearched = true;
          });
          return;
        }
        
        const startTime = Date.now();
        
        // Build search parameters
        const params = new URLSearchParams({
          q: searchQuery,
          page: page.toString(),
          limit: limit.toString(),
          ...Object.entries(filters).reduce((acc, [key, value]) => {
            if (value !== undefined && value !== null) {
              if (Array.isArray(value)) {
                acc[key] = value.join(',');
              } else {
                acc[key] = value.toString();
              }
            }
            return acc;
          }, {} as Record<string, string>),
        });
        
        const response = await fetch(`/api/search?${params}`);
        
        if (!response.ok) {
          throw new Error('Search failed');
        }
        
        const results: SearchResults = await response.json();
        const searchDuration = Date.now() - startTime;
        
        // Update state
        set((state) => {
          state.query = searchQuery;
          state.results = results;
          state.hasSearched = true;
          state.searchMetrics = {
            lastSearchTime: Date.now(),
            searchDuration,
            totalSearches: state.searchMetrics.totalSearches + 1,
          };
          
          // Cache results
          state.searchCache.set(cacheKey, {
            results,
            timestamp: Date.now(),
          });
          
          // Clean old cache entries (keep last 50)
          if (state.searchCache.size > 50) {
            const entries = Array.from(state.searchCache.entries());
            entries.sort(([, a], [, b]) => b.timestamp - a.timestamp);
            state.searchCache.clear();
            entries.slice(0, 50).forEach(([key, value]) => {
              state.searchCache.set(key, value);
            });
          }
        });
        
        // Add to history
        if (searchQuery.trim()) {
          get().addToHistory(searchQuery);
        }
        
        // Track search
        get().trackSearch(searchQuery, results.total);
      }),
      
      clearSearch: () => {
        set((state) => {
          state.query = '';
          state.results = null;
          state.hasSearched = false;
          state.filters = {};
        });
      },
      
      // Advanced search
      advancedSearch: withAsyncAction<SearchStore>('SearchStore', 'advancedSearch')(async function(
        filters: SearchFilters, 
        query?: string
      ) {
        set((state) => {
          state.filters = filters;
          if (query !== undefined) {
            state.query = query;
          }
        });
        
        await get().search(query);
      }),
      
      toggleAdvancedMode: () => {
        set((state) => {
          state.advancedMode = !state.advancedMode;
        });
      },
      
      // Filters
      setFilters: (filters: Partial<SearchFilters>) => {
        set((state) => {
          state.filters = { ...state.filters, ...filters };
        });
      },
      
      clearFilters: () => {
        set((state) => {
          state.filters = {};
        });
      },
      
      saveFilters: (name: string, filters: SearchFilters) => {
        set((state) => {
          state.savedFilters[name] = filters;
        });
      },
      
      loadSavedFilters: (name: string) => {
        const savedFilters = get().savedFilters[name];
        if (savedFilters) {
          set((state) => {
            state.filters = savedFilters;
          });
        }
      },
      
      deleteSavedFilters: (name: string) => {
        set((state) => {
          delete state.savedFilters[name];
        });
      },
      
      // Suggestions
      getSuggestions: withAsyncAction<SearchStore>('SearchStore', 'getSuggestions')(async function(query: string) {
        if (!query.trim()) {
          set((state) => {
            state.suggestions = [];
          });
          return;
        }
        
        set((state) => {
          state.suggestionsLoading = true;
        });
        
        const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch suggestions');
        }
        
        const suggestions: SearchSuggestion[] = await response.json();
        
        set((state) => {
          state.suggestions = suggestions;
          state.suggestionsLoading = false;
        });
      }),
      
      clearSuggestions: () => {
        set((state) => {
          state.suggestions = [];
        });
      },
      
      // History
      addToHistory: (query: string) => {
        if (!query.trim()) return;
        
        set((state) => {
          const history = state.searchHistory.filter(item => item !== query);
          history.unshift(query);
          state.searchHistory = history.slice(0, 20); // Keep last 20 searches
        });
      },
      
      removeFromHistory: (query: string) => {
        set((state) => {
          state.searchHistory = state.searchHistory.filter(item => item !== query);
        });
      },
      
      clearHistory: () => {
        set((state) => {
          state.searchHistory = [];
        });
      },
      
      // Popular searches
      fetchPopularSearches: withAsyncAction<SearchStore>('SearchStore', 'fetchPopularSearches')(async function() {
        set((state) => {
          state.popularLoading = true;
        });
        
        const response = await fetch('/api/search/popular');
        
        if (!response.ok) {
          throw new Error('Failed to fetch popular searches');
        }
        
        const popularSearches: string[] = await response.json();
        
        set((state) => {
          state.popularSearches = popularSearches;
          state.popularLoading = false;
        });
      }),
      
      // Pagination
      loadMore: async () => {
        const results = get().results;
        if (!results || !results.hasNextPage) return;
        
        const nextPage = results.page + 1;
        const response = await get().search(get().query, { 
          page: nextPage, 
          limit: results.limit 
        });
        
        // Append results instead of replacing
        if (response) {
          set((state) => {
            if (state.results) {
              state.results.products = [...state.results.products, ...results.products];
              state.results.page = nextPage;
              state.results.hasNextPage = results.hasNextPage;
            }
          });
        }
      },
      
      goToPage: async (page: number) => {
        await get().search(get().query, { page });
      },
      
      // Utilities
      getSearchUrl: (query?: string, filters?: SearchFilters) => {
        const searchQuery = query || get().query;
        const searchFilters = filters || get().filters;
        
        const params = new URLSearchParams();
        if (searchQuery) params.set('q', searchQuery);
        
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            if (Array.isArray(value)) {
              params.set(key, value.join(','));
            } else {
              params.set(key, value.toString());
            }
          }
        });
        
        return `/search?${params.toString()}`;
      },
      
      exportResults: withAsyncAction<SearchStore>('SearchStore', 'exportResults')(async function(format: 'csv' | 'json') {
        const results = get().results;
        if (!results) throw new Error('No search results to export');
        
        const response = await fetch('/api/search/export', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: get().query,
            filters: get().filters,
            format,
          }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to export results');
        }
        
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `search-results-${Date.now()}.${format}`;
        a.click();
        URL.revokeObjectURL(url);
      }),
      
      // Analytics
      trackSearch: (query: string, resultCount: number) => {
        // Track search analytics (could send to analytics service)
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'search', {
            search_term: query,
            result_count: resultCount,
          });
        }
        
        // Store local analytics
        const analytics = JSON.parse(localStorage.getItem('search-analytics') || '[]');
        analytics.push({
          query,
          resultCount,
          timestamp: Date.now(),
          filters: get().filters,
        });
        
        // Keep last 1000 entries
        if (analytics.length > 1000) {
          analytics.splice(0, analytics.length - 1000);
        }
        
        localStorage.setItem('search-analytics', JSON.stringify(analytics));
      },
      
      getSearchAnalytics: () => {
        if (typeof window === 'undefined') return [];
        return JSON.parse(localStorage.getItem('search-analytics') || '[]');
      },
    })
  )
);

/**
 * Search store selectors for optimized re-renders
 */
export const searchSelectors = {
  query: (state: SearchStore) => state.query,
  results: (state: SearchStore) => state.results,
  loading: (state: SearchStore) => state.loading,
  error: (state: SearchStore) => state.error,
  hasSearched: (state: SearchStore) => state.hasSearched,
  suggestions: (state: SearchStore) => state.suggestions,
  suggestionsLoading: (state: SearchStore) => state.suggestionsLoading,
  searchHistory: (state: SearchStore) => state.searchHistory,
  popularSearches: (state: SearchStore) => state.popularSearches,
  filters: (state: SearchStore) => state.filters,
  advancedMode: (state: SearchStore) => state.advancedMode,
  hasResults: (state: SearchStore) => state.results && state.results.total > 0,
  totalResults: (state: SearchStore) => state.results?.total || 0,
  hasNextPage: (state: SearchStore) => state.results?.hasNextPage || false,
  isSearching: (state: SearchStore) => state.loading,
  searchMetrics: (state: SearchStore) => state.searchMetrics,
};

/**
 * Export store instance for external use
 */
export const searchStore = useSearchStore.getState();