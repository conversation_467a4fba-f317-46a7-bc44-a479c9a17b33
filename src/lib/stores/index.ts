/**
 * Centralized store exports
 */

// Store instances
export { useCartStore, cartSelectors } from './CartStore';
export { useUserStore, userSelectors } from './UserStore';  
export { useSearchStore, searchSelectors } from './SearchStore';
export { useAppStore, appSelectors } from './AppStore';

// Store types
export type { CartStore } from './CartStore';
export type { UserStore } from './UserStore';
export type { SearchStore, SearchFilters } from './SearchStore';
export type { AppStore } from './AppStore';

// Base store utilities
export { 
  createBaseStore, 
  withAsyncAction, 
  withOptimisticUpdate, 
  shouldRefetch 
} from './base/BaseStore';

export type { 
  BaseStore, 
  BaseActions 
} from './base/BaseStore';