import { create } from 'zustand';
import { BaseStore, createPersistedStore, withAsyncAction } from './base/BaseStore';

/**
 * Global application state interface
 */
interface AppState extends BaseStore {
  // UI State
  theme: 'light' | 'dark' | 'system';
  language: 'vi' | 'en';
  currency: 'VND' | 'USD';
  
  // Layout State
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  
  // Feature Flags
  features: {
    enableNotifications: boolean;
    enableAnalytics: boolean;
    enableBetaFeatures: boolean;
    maintenanceMode: boolean;
  };
  
  // Application Status
  isOnline: boolean;
  version: string;
  lastUpdate: number;
  
  // Performance Metrics
  performanceMetrics: {
    averageLoadTime: number;
    errorCount: number;
    sessionDuration: number;
  };
  
  // Notifications
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: number;
    read: boolean;
    persistent?: boolean;
  }>;
  
  // Modal State
  modals: {
    loginModal: boolean;
    cartModal: boolean;
    searchModal: boolean;
    [key: string]: boolean;
  };
  
  // Global Loading States
  globalLoading: {
    app: boolean;
    auth: boolean;
    data: boolean;
  };
}

/**
 * Application actions interface
 */
interface AppActions {
  // Theme Management
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleTheme: () => void;
  
  // Language & Currency
  setLanguage: (language: 'vi' | 'en') => void;
  setCurrency: (currency: 'VND' | 'USD') => void;
  
  // Layout Actions
  toggleSidebar: () => void;
  closeSidebar: () => void;
  toggleMobileMenu: () => void;
  closeMobileMenu: () => void;
  
  // Feature Flags
  updateFeatureFlag: (flag: keyof AppState['features'], enabled: boolean) => void;
  loadFeatureFlags: () => Promise<void>;
  
  // Application Status
  setOnlineStatus: (isOnline: boolean) => void;
  updateVersion: (version: string) => void;
  
  // Performance Tracking
  recordLoadTime: (loadTime: number) => void;
  incrementErrorCount: () => void;
  updateSessionDuration: (duration: number) => void;
  
  // Notification Management
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp' | 'read'>) => void;
  markNotificationAsRead: (id: string) => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
  clearReadNotifications: () => void;
  
  // Modal Management
  openModal: (modalName: string) => void;
  closeModal: (modalName: string) => void;
  closeAllModals: () => void;
  
  // Global Loading
  setGlobalLoading: (type: keyof AppState['globalLoading'], loading: boolean) => void;
  
  // Application Lifecycle
  initializeApp: () => Promise<void>;
  refreshApp: () => Promise<void>;
  resetApp: () => void;
}

/**
 * Combined app store interface
 */
export interface AppStore extends AppState, AppActions {}

/**
 * Initial app state
 */
const initialAppState: Omit<AppState, keyof BaseStore> = {
  theme: 'system',
  language: 'vi',
  currency: 'VND',
  sidebarOpen: false,
  mobileMenuOpen: false,
  features: {
    enableNotifications: true,
    enableAnalytics: true,
    enableBetaFeatures: false,
    maintenanceMode: false,
  },
  isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
  version: '1.0.0',
  lastUpdate: Date.now(),
  performanceMetrics: {
    averageLoadTime: 0,
    errorCount: 0,
    sessionDuration: 0,
  },
  notifications: [],
  modals: {
    loginModal: false,
    cartModal: false,
    searchModal: false,
  },
  globalLoading: {
    app: false,
    auth: false,
    data: false,
  },
};

/**
 * Create app store with persistence
 */
export const useAppStore = create<AppStore>(
  createPersistedStore<AppStore>(
    'AppStore',
    initialAppState,
    (set, get) => ({
      // Theme Management
      setTheme: (theme) => {
        set((state) => {
          state.theme = theme;
        });
        
        // Apply theme to document
        if (typeof document !== 'undefined') {
          const root = document.documentElement;
          if (theme === 'system') {
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            root.classList.toggle('dark', prefersDark);
          } else {
            root.classList.toggle('dark', theme === 'dark');
          }
        }
      },
      
      toggleTheme: () => {
        const currentTheme = get().theme;
        const nextTheme = currentTheme === 'light' ? 'dark' : 'light';
        get().setTheme(nextTheme);
      },
      
      // Language & Currency
      setLanguage: (language) => {
        set((state) => {
          state.language = language;
        });
        
        // Update document language
        if (typeof document !== 'undefined') {
          document.documentElement.lang = language;
        }
      },
      
      setCurrency: (currency) => {
        set((state) => {
          state.currency = currency;
        });
      },
      
      // Layout Actions
      toggleSidebar: () => {
        set((state) => {
          state.sidebarOpen = !state.sidebarOpen;
        });
      },
      
      closeSidebar: () => {
        set((state) => {
          state.sidebarOpen = false;
        });
      },
      
      toggleMobileMenu: () => {
        set((state) => {
          state.mobileMenuOpen = !state.mobileMenuOpen;
        });
      },
      
      closeMobileMenu: () => {
        set((state) => {
          state.mobileMenuOpen = false;
        });
      },
      
      // Feature Flags
      updateFeatureFlag: (flag, enabled) => {
        set((state) => {
          state.features[flag] = enabled;
        });
      },
      
      loadFeatureFlags: withAsyncAction<AppStore>('AppStore', 'loadFeatureFlags')(async function() {
        const response = await fetch('/api/feature-flags');
        
        if (!response.ok) {
          throw new Error('Failed to load feature flags');
        }
        
        const features = await response.json();
        
        set((state) => {
          state.features = { ...state.features, ...features };
        });
      }),
      
      // Application Status
      setOnlineStatus: (isOnline) => {
        set((state) => {
          state.isOnline = isOnline;
        });
        
        // Show notification for status change
        if (!isOnline) {
          get().addNotification({
            type: 'warning',
            title: 'Connection Lost',
            message: 'You are currently offline. Some features may not work.',
            persistent: true,
          });
        } else {
          // Remove offline notifications
          const offlineNotifications = get().notifications.filter(n => 
            n.message.includes('offline') || n.message.includes('Connection Lost')
          );
          offlineNotifications.forEach(n => get().removeNotification(n.id));
        }
      },
      
      updateVersion: (version) => {
        set((state) => {
          state.version = version;
          state.lastUpdate = Date.now();
        });
      },
      
      // Performance Tracking
      recordLoadTime: (loadTime) => {
        set((state) => {
          const currentAverage = state.performanceMetrics.averageLoadTime;
          const newAverage = currentAverage === 0 ? loadTime : (currentAverage + loadTime) / 2;
          state.performanceMetrics.averageLoadTime = newAverage;
        });
      },
      
      incrementErrorCount: () => {
        set((state) => {
          state.performanceMetrics.errorCount += 1;
        });
      },
      
      updateSessionDuration: (duration) => {
        set((state) => {
          state.performanceMetrics.sessionDuration = duration;
        });
      },
      
      // Notification Management
      addNotification: (notification) => {
        const id = `notification-${Date.now()}-${Math.random()}`;
        
        set((state) => {
          state.notifications.unshift({
            ...notification,
            id,
            timestamp: Date.now(),
            read: false,
          });
          
          // Keep only last 50 notifications
          if (state.notifications.length > 50) {
            state.notifications = state.notifications.slice(0, 50);
          }
        });
        
        // Auto-remove non-persistent notifications after 5 seconds
        if (!notification.persistent) {
          setTimeout(() => {
            get().removeNotification(id);
          }, 5000);
        }
      },
      
      markNotificationAsRead: (id) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === id);
          if (notification) {
            notification.read = true;
          }
        });
      },
      
      removeNotification: (id) => {
        set((state) => {
          state.notifications = state.notifications.filter(n => n.id !== id);
        });
      },
      
      clearAllNotifications: () => {
        set((state) => {
          state.notifications = [];
        });
      },
      
      clearReadNotifications: () => {
        set((state) => {
          state.notifications = state.notifications.filter(n => !n.read);
        });
      },
      
      // Modal Management
      openModal: (modalName) => {
        set((state) => {
          state.modals[modalName] = true;
        });
      },
      
      closeModal: (modalName) => {
        set((state) => {
          state.modals[modalName] = false;
        });
      },
      
      closeAllModals: () => {
        set((state) => {
          Object.keys(state.modals).forEach(modal => {
            state.modals[modal] = false;
          });
        });
      },
      
      // Global Loading
      setGlobalLoading: (type, loading) => {
        set((state) => {
          state.globalLoading[type] = loading;
        });
      },
      
      // Application Lifecycle
      initializeApp: withAsyncAction<AppStore>('AppStore', 'initializeApp')(async function() {
        // Load feature flags
        await get().loadFeatureFlags();
        
        // Setup online/offline listeners
        if (typeof window !== 'undefined') {
          const handleOnline = () => get().setOnlineStatus(true);
          const handleOffline = () => get().setOnlineStatus(false);
          
          window.addEventListener('online', handleOnline);
          window.addEventListener('offline', handleOffline);
        }
        
        // Setup theme listener
        if (typeof window !== 'undefined' && get().theme === 'system') {
          const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
          const handleThemeChange = (e: MediaQueryListEvent) => {
            if (get().theme === 'system') {
              document.documentElement.classList.toggle('dark', e.matches);
            }
          };
          
          mediaQuery.addEventListener('change', handleThemeChange);
        }
        
        // Record initial load time
        if (typeof window !== 'undefined' && window.performance) {
          const loadTime = window.performance.now();
          get().recordLoadTime(loadTime);
        }
        
        // Start session duration tracking
        const sessionStart = Date.now();
        const updateSession = () => {
          const duration = Date.now() - sessionStart;
          get().updateSessionDuration(duration);
        };
        
        setInterval(updateSession, 60000); // Update every minute
      }),
      
      refreshApp: withAsyncAction<AppStore>('AppStore', 'refreshApp')(async function() {
        // Reload feature flags
        await get().loadFeatureFlags();
        
        // Update last refresh time
        set((state) => {
          state.lastUpdate = Date.now();
        });
        
        // Clear errors
        get().setError(null);
      }),
      
      resetApp: () => {
        set((state) => {
          // Reset to initial state but preserve some settings
          const preservedSettings = {
            theme: state.theme,
            language: state.language,
            currency: state.currency,
          };
          
          Object.assign(state, initialAppState, preservedSettings);
        });
      },
    }),
    {
      name: 'app-store',
      version: 1,
      partialize: (state) => ({
        theme: state.theme,
        language: state.language,
        currency: state.currency,
        features: state.features,
        sidebarOpen: state.sidebarOpen,
      }),
    }
  )
);

/**
 * App store selectors for optimized re-renders
 */
export const appSelectors = {
  theme: (state: AppStore) => state.theme,
  language: (state: AppStore) => state.language,
  currency: (state: AppStore) => state.currency,
  sidebarOpen: (state: AppStore) => state.sidebarOpen,
  mobileMenuOpen: (state: AppStore) => state.mobileMenuOpen,
  features: (state: AppStore) => state.features,
  isOnline: (state: AppStore) => state.isOnline,
  notifications: (state: AppStore) => state.notifications,
  unreadNotifications: (state: AppStore) => state.notifications.filter(n => !n.read),
  modals: (state: AppStore) => state.modals,
  globalLoading: (state: AppStore) => state.globalLoading,
  isAnyLoading: (state: AppStore) => Object.values(state.globalLoading).some(Boolean),
  performanceMetrics: (state: AppStore) => state.performanceMetrics,
};

/**
 * Export store instance for external use
 */
export const appStore = useAppStore.getState();