import { create } from 'zustand';
import { BaseStore, createBaseStore, withAsyncAction, withOptimisticUpdate, shouldRefetch } from './base/BaseStore';
import { Cart, CartItem, Product } from '@/types/core/entities';
import { toast } from 'sonner';

/**
 * Cart store state interface
 */
interface CartState extends BaseStore {
  // Cart data
  cart: Cart | null;
  isOpen: boolean;
  
  // Derived data
  summary: {
    subtotal: number;
    tax: number;
    shipping: number;
    total: number;
    itemCount: number;
    totalQuantity: number;
  } | null;
  
  // Optimistic updates
  optimisticUpdates: Map<string, { type: 'add' | 'update' | 'remove'; data: any }>;
  
  // Cache control
  lastFetch?: number;
}

/**
 * Cart store actions interface
 */
interface CartActions {
  // Cart management
  fetchCart: () => Promise<void>;
  openCart: () => void;
  closeCart: () => void;
  toggleCart: () => void;
  
  // Item management
  addToCart: (productId: string, quantity: number, variantId?: string) => Promise<void>;
  updateCartItem: (itemId: string, quantity: number) => Promise<void>;
  removeFromCart: (itemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  
  // Bulk operations
  addMultipleItems: (items: Array<{ productId: string; quantity: number; variantId?: string }>) => Promise<void>;
  updateMultipleItems: (updates: Array<{ itemId: string; quantity: number }>) => Promise<void>;
  
  // Utilities
  getItemQuantity: (productId: string) => number;
  isInCart: (productId: string) => boolean;
  canAddToCart: (productId: string, quantity: number) => boolean;
  
  // Quick actions
  quickAdd: (productId: string, quantity?: number) => Promise<void>;
  moveToWishlist: (itemId: string) => Promise<void>;
  
  // Calculations
  calculateSummary: () => void;
  applyDiscount: (code: string) => Promise<void>;
  removeDiscount: () => Promise<void>;
  
  // Validation
  validateCart: () => Promise<{ isValid: boolean; errors: string[] }>;
  
  // Sync
  syncWithServer: () => Promise<void>;
}

/**
 * Combined cart store interface
 */
export interface CartStore extends CartState, CartActions {}

/**
 * Initial cart state
 */
const initialCartState: Omit<CartState, keyof BaseStore> = {
  cart: null,
  isOpen: false,
  summary: null,
  optimisticUpdates: new Map(),
};

/**
 * Calculate cart summary from cart data
 */
function calculateCartSummary(cart: Cart | null) {
  if (!cart || !cart.items.length) {
    return null;
  }
  
  const subtotal = cart.items.reduce((sum, item) => {
    const price = item.product.salePrice || item.product.price;
    return sum + (price * item.quantity);
  }, 0);
  
  const itemCount = cart.items.length;
  const totalQuantity = cart.items.reduce((sum, item) => sum + item.quantity, 0);
  
  // Calculate tax (8% for example)
  const tax = subtotal * 0.08;
  
  // Calculate shipping (free over $100)
  const shipping = subtotal > 100 ? 0 : 15;
  
  const total = subtotal + tax + shipping;
  
  return {
    subtotal,
    tax,
    shipping,
    total,
    itemCount,
    totalQuantity,
  };
}

/**
 * Create cart store
 */
export const useCartStore = create<CartStore>(
  createBaseStore<CartStore>(
    'CartStore',
    initialCartState,
    (set, get) => ({
      // Cart management
      fetchCart: withAsyncAction<CartStore>('CartStore', 'fetchCart')(async function() {
        // Check cache freshness
        if (!shouldRefetch(get().lastFetch, 2 * 60 * 1000)) {
          return;
        }
        
        const response = await fetch('/api/cart', {
          headers: {
            'Content-Type': 'application/json',
          },
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch cart');
        }
        
        const cart = await response.json();
        
        set((state) => {
          state.cart = cart;
          state.summary = calculateCartSummary(cart);
          state.lastFetch = Date.now();
        });
      }),
      
      openCart: () => set((state) => { state.isOpen = true; }),
      closeCart: () => set((state) => { state.isOpen = false; }),
      toggleCart: () => set((state) => { state.isOpen = !state.isOpen; }),
      
      // Item management with optimistic updates
      addToCart: withAsyncAction<CartStore>('CartStore', 'addToCart')(async function(productId: string, quantity: number = 1, variantId?: string) {
        const optimisticUpdate = (state: CartStore) => {
          // Add optimistic update tracking
          state.optimisticUpdates.set(productId, { type: 'add', data: { productId, quantity, variantId } });
          
          // If cart exists, optimistically add item
          if (state.cart) {
            // Check if item already exists
            const existingItemIndex = state.cart.items.findIndex(item => 
              item.productId === productId && item.variantId === variantId
            );
            
            if (existingItemIndex >= 0) {
              // Update existing item
              state.cart.items[existingItemIndex].quantity += quantity;
            } else {
              // Add new item (we'll need product data from server)
              // For now, just mark as adding
              state.loading = true;
            }
            
            state.summary = calculateCartSummary(state.cart);
          }
        };
        
        const revertUpdate = (state: CartStore) => {
          state.optimisticUpdates.delete(productId);
          // Revert optimistic changes - would need more complex logic for real implementation
        };
        
        const serverUpdate = async () => {
          const response = await fetch('/api/cart', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ productId, quantity, variantId }),
          });
          
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to add item to cart');
          }
          
          return await response.json();
        };
        
        return withOptimisticUpdate(optimisticUpdate, revertUpdate, serverUpdate).call(this);
      }),
      
      updateCartItem: withAsyncAction<CartStore>('CartStore', 'updateCartItem')(async function(itemId: string, quantity: number) {
        const optimisticUpdate = (state: CartStore) => {
          if (state.cart) {
            const itemIndex = state.cart.items.findIndex(item => item.id === itemId);
            if (itemIndex >= 0) {
              if (quantity <= 0) {
                state.cart.items.splice(itemIndex, 1);
              } else {
                state.cart.items[itemIndex].quantity = quantity;
              }
              state.summary = calculateCartSummary(state.cart);
            }
          }
        };
        
        const revertUpdate = (state: CartStore) => {
          // Would need to store previous state for proper revert
          get().fetchCart();
        };
        
        const serverUpdate = async () => {
          const response = await fetch(`/api/cart/${itemId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ quantity }),
          });
          
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to update cart item');
          }
          
          return await response.json();
        };
        
        return withOptimisticUpdate(optimisticUpdate, revertUpdate, serverUpdate).call(this);
      }),
      
      removeFromCart: withAsyncAction<CartStore>('CartStore', 'removeFromCart')(async function(itemId: string) {
        const optimisticUpdate = (state: CartStore) => {
          if (state.cart) {
            const itemIndex = state.cart.items.findIndex(item => item.id === itemId);
            if (itemIndex >= 0) {
              state.cart.items.splice(itemIndex, 1);
              state.summary = calculateCartSummary(state.cart);
            }
          }
        };
        
        const revertUpdate = (state: CartStore) => {
          get().fetchCart();
        };
        
        const serverUpdate = async () => {
          const response = await fetch(`/api/cart/${itemId}`, {
            method: 'DELETE',
          });
          
          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Failed to remove item from cart');
          }
          
          toast.success('Item removed from cart');
          return await response.json();
        };
        
        return withOptimisticUpdate(optimisticUpdate, revertUpdate, serverUpdate).call(this);
      }),
      
      clearCart: withAsyncAction<CartStore>('CartStore', 'clearCart')(async function() {
        const response = await fetch('/api/cart', {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('Failed to clear cart');
        }
        
        set((state) => {
          state.cart = null;
          state.summary = null;
          state.optimisticUpdates.clear();
        });
        
        toast.success('Cart cleared');
      }),
      
      // Bulk operations
      addMultipleItems: withAsyncAction<CartStore>('CartStore', 'addMultipleItems')(async function(items: Array<{ productId: string; quantity: number; variantId?: string }>) {
        const response = await fetch('/api/cart/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ items }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to add items to cart');
        }
        
        const cart = await response.json();
        
        set((state) => {
          state.cart = cart;
          state.summary = calculateCartSummary(cart);
        });
        
        toast.success(`Added ${items.length} items to cart`);
      }),
      
      updateMultipleItems: withAsyncAction<CartStore>('CartStore', 'updateMultipleItems')(async function(updates: Array<{ itemId: string; quantity: number }>) {
        const response = await fetch('/api/cart/bulk', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ updates }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to update cart items');
        }
        
        const cart = await response.json();
        
        set((state) => {
          state.cart = cart;
          state.summary = calculateCartSummary(cart);
        });
      }),
      
      // Utility functions
      getItemQuantity: (productId: string) => {
        const cart = get().cart;
        if (!cart) return 0;
        
        const item = cart.items.find(item => item.productId === productId);
        return item ? item.quantity : 0;
      },
      
      isInCart: (productId: string) => {
        return get().getItemQuantity(productId) > 0;
      },
      
      canAddToCart: (productId: string, quantity: number) => {
        const cart = get().cart;
        if (!cart) return true;
        
        const currentQuantity = get().getItemQuantity(productId);
        const item = cart.items.find(item => item.productId === productId);
        
        if (!item) return true;
        
        // Check stock availability
        const product = item.product as any;
        if (product && 'stock' in product) {
          return currentQuantity + quantity <= product.stock;
        }
        
        return true;
      },
      
      // Quick actions
      quickAdd: async (productId: string, quantity: number = 1) => {
        await get().addToCart(productId, quantity);
        get().openCart();
      },
      
      moveToWishlist: withAsyncAction<CartStore>('CartStore', 'moveToWishlist')(async function(itemId: string) {
        // Implementation would depend on wishlist API
        await get().removeFromCart(itemId);
        toast.success('Item moved to wishlist');
      }),
      
      // Calculations
      calculateSummary: () => {
        const cart = get().cart;
        set((state) => {
          state.summary = calculateCartSummary(cart);
        });
      },
      
      applyDiscount: withAsyncAction<CartStore>('CartStore', 'applyDiscount')(async function(code: string) {
        const response = await fetch('/api/cart/discount', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ code }),
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Invalid discount code');
        }
        
        const cart = await response.json();
        
        set((state) => {
          state.cart = cart;
          state.summary = calculateCartSummary(cart);
        });
        
        toast.success('Discount applied successfully');
      }),
      
      removeDiscount: withAsyncAction<CartStore>('CartStore', 'removeDiscount')(async function() {
        const response = await fetch('/api/cart/discount', {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('Failed to remove discount');
        }
        
        const cart = await response.json();
        
        set((state) => {
          state.cart = cart;
          state.summary = calculateCartSummary(cart);
        });
      }),
      
      // Validation
      validateCart: withAsyncAction<CartStore>('CartStore', 'validateCart')(async function() {
        const response = await fetch('/api/cart/validate');
        
        if (!response.ok) {
          throw new Error('Failed to validate cart');
        }
        
        const validation = await response.json();
        return validation;
      }),
      
      // Sync with server
      syncWithServer: withAsyncAction<CartStore>('CartStore', 'syncWithServer')(async function() {
        await get().fetchCart();
      }),
    })
  )
);

/**
 * Cart store selectors for optimized re-renders
 */
export const cartSelectors = {
  cart: (state: CartStore) => state.cart,
  isOpen: (state: CartStore) => state.isOpen,
  loading: (state: CartStore) => state.loading,
  error: (state: CartStore) => state.error,
  summary: (state: CartStore) => state.summary,
  itemCount: (state: CartStore) => state.cart?.items.length || 0,
  totalQuantity: (state: CartStore) => state.summary?.totalQuantity || 0,
  isEmpty: (state: CartStore) => !state.cart || state.cart.items.length === 0,
  hasItems: (state: CartStore) => state.cart && state.cart.items.length > 0,
};

/**
 * Export store instance for external use
 */
export const cartStore = useCartStore.getState();