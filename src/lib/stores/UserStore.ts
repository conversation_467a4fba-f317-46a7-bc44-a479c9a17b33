import { create } from 'zustand';
import { BaseStore, createPersistedStore, withAsyncAction, shouldRefetch } from './base/BaseStore';
import { User, Address } from '@/types/core/entities';
import { toast } from 'sonner';

/**
 * User preferences interface
 */
interface UserPreferences {
  language: string;
  currency: string;
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    showProfile: boolean;
    showOrders: boolean;
    allowDataCollection: boolean;
  };
  shopping: {
    savePaymentMethods: boolean;
    oneClickCheckout: boolean;
    wishlistPublic: boolean;
  };
}

/**
 * User store state interface
 */
interface UserState extends BaseStore {
  // User data
  user: User | null;
  isAuthenticated: boolean;
  
  // User addresses
  addresses: Address[];
  addressesLoading: boolean;
  addressesError: string | null;
  
  // Wishlist
  wishlistItems: string[]; // Product IDs
  wishlistLoading: boolean;
  wishlistError: string | null;
  
  // User preferences
  preferences: UserPreferences | null;
  preferencesLoading: boolean;
  
  // Profile completion
  profileMetrics: {
    completionPercentage: number;
    missingFields: string[];
    isComplete: boolean;
  };
  
  // Session data
  sessionData: {
    lastActivity: number;
    loginTime: number;
    deviceInfo: string;
  } | null;
  
  // Cache control
  lastUserFetch?: number;
  lastAddressesFetch?: number;
  lastWishlistFetch?: number;
}

/**
 * User store actions interface
 */
interface UserActions {
  // Authentication
  setUser: (user: User | null) => void;
  fetchUser: () => Promise<void>;
  refreshUser: () => Promise<void>;
  logout: () => Promise<void>;
  
  // Profile management
  updateProfile: (data: Partial<User>) => Promise<void>;
  uploadAvatar: (file: File) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  
  // Address management
  fetchAddresses: () => Promise<void>;
  addAddress: (address: Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateAddress: (id: string, data: Partial<Address>) => Promise<void>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string) => Promise<void>;
  
  // Wishlist management
  fetchWishlist: () => Promise<void>;
  addToWishlist: (productId: string) => Promise<void>;
  removeFromWishlist: (productId: string) => Promise<void>;
  clearWishlist: () => Promise<void>;
  isInWishlist: (productId: string) => boolean;
  
  // Preferences
  updatePreferences: (preferences: Partial<UserPreferences>) => Promise<void>;
  resetPreferences: () => Promise<void>;
  
  // Profile utilities
  calculateProfileCompletion: () => void;
  getDefaultAddress: () => Address | null;
  
  // Security
  enable2FA: (phone: string) => Promise<void>;
  disable2FA: () => Promise<void>;
  updateSecuritySettings: (settings: any) => Promise<void>;
  
  // Data management
  requestDataExport: () => Promise<void>;
  deleteAccount: (password: string) => Promise<void>;
  
  // Session management
  updateLastActivity: () => void;
  extendSession: () => Promise<void>;
}

/**
 * Combined user store interface
 */
export interface UserStore extends UserState, UserActions {}

/**
 * Default user preferences
 */
const defaultPreferences: UserPreferences = {
  language: 'vi',
  currency: 'VND',
  theme: 'system',
  notifications: {
    email: true,
    sms: false,
    push: true,
    marketing: false,
  },
  privacy: {
    showProfile: false,
    showOrders: false,
    allowDataCollection: false,
  },
  shopping: {
    savePaymentMethods: false,
    oneClickCheckout: false,
    wishlistPublic: false,
  },
};

/**
 * Initial user state
 */
const initialUserState: Omit<UserState, keyof BaseStore> = {
  user: null,
  isAuthenticated: false,
  addresses: [],
  addressesLoading: false,
  addressesError: null,
  wishlistItems: [],
  wishlistLoading: false,
  wishlistError: null,
  preferences: defaultPreferences,
  preferencesLoading: false,
  profileMetrics: {
    completionPercentage: 0,
    missingFields: [],
    isComplete: false,
  },
  sessionData: null,
};

/**
 * Calculate profile completion percentage
 */
function calculateProfileCompletion(user: User | null) {
  if (!user) {
    return {
      completionPercentage: 0,
      missingFields: ['All profile fields'],
      isComplete: false,
    };
  }
  
  const requiredFields = [
    { key: 'name', label: 'Full Name' },
    { key: 'email', label: 'Email' },
    { key: 'phone', label: 'Phone Number' },
    { key: 'dateOfBirth', label: 'Date of Birth' },
    { key: 'gender', label: 'Gender' },
    { key: 'avatar', label: 'Profile Picture' },
  ];
  
  const completedFields = requiredFields.filter(field => 
    user[field.key as keyof User] !== null && 
    user[field.key as keyof User] !== undefined && 
    user[field.key as keyof User] !== ''
  );
  
  const missingFields = requiredFields
    .filter(field => 
      user[field.key as keyof User] === null || 
      user[field.key as keyof User] === undefined || 
      user[field.key as keyof User] === ''
    )
    .map(field => field.label);
  
  const completionPercentage = Math.round((completedFields.length / requiredFields.length) * 100);
  
  return {
    completionPercentage,
    missingFields,
    isComplete: completionPercentage === 100,
  };
}

/**
 * Create user store with persistence
 */
export const useUserStore = create<UserStore>(
  createPersistedStore<UserStore>(
    'UserStore',
    initialUserState,
    (set, get) => ({
      // Authentication
      setUser: (user: User | null) => {
        set((state) => {
          state.user = user;
          state.isAuthenticated = !!user;
          state.profileMetrics = calculateProfileCompletion(user);
          
          if (user) {
            state.sessionData = {
              lastActivity: Date.now(),
              loginTime: Date.now(),
              deviceInfo: navigator.userAgent,
            };
          } else {
            state.sessionData = null;
          }
        });
      },
      
      fetchUser: withAsyncAction<UserStore>('UserStore', 'fetchUser')(async function() {
        if (!shouldRefetch(get().lastUserFetch, 5 * 60 * 1000)) {
          return;
        }
        
        const response = await fetch('/api/auth/me');
        
        if (!response.ok) {
          if (response.status === 401) {
            get().setUser(null);
            return;
          }
          throw new Error('Failed to fetch user data');
        }
        
        const user = await response.json();
        
        set((state) => {
          state.user = user;
          state.isAuthenticated = true;
          state.profileMetrics = calculateProfileCompletion(user);
          state.lastUserFetch = Date.now();
        });
      }),
      
      refreshUser: async () => {
        set((state) => { state.lastUserFetch = undefined; });
        await get().fetchUser();
      },
      
      logout: withAsyncAction<UserStore>('UserStore', 'logout')(async function() {
        await fetch('/api/auth/logout', { method: 'POST' });
        
        set((state) => {
          state.user = null;
          state.isAuthenticated = false;
          state.addresses = [];
          state.wishlistItems = [];
          state.sessionData = null;
          state.profileMetrics = calculateProfileCompletion(null);
        });
        
        toast.success('Logged out successfully');
      }),
      
      // Profile management
      updateProfile: withAsyncAction<UserStore>('UserStore', 'updateProfile')(async function(data: Partial<User>) {
        const response = await fetch('/api/user/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to update profile');
        }
        
        const updatedUser = await response.json();
        
        set((state) => {
          state.user = updatedUser;
          state.profileMetrics = calculateProfileCompletion(updatedUser);
        });
        
        toast.success('Profile updated successfully');
      }),
      
      uploadAvatar: withAsyncAction<UserStore>('UserStore', 'uploadAvatar')(async function(file: File) {
        const formData = new FormData();
        formData.append('avatar', file);
        
        const response = await fetch('/api/user/avatar', {
          method: 'POST',
          body: formData,
        });
        
        if (!response.ok) {
          throw new Error('Failed to upload avatar');
        }
        
        const { avatarUrl } = await response.json();
        
        set((state) => {
          if (state.user) {
            state.user.avatar = avatarUrl;
            state.profileMetrics = calculateProfileCompletion(state.user);
          }
        });
        
        toast.success('Avatar updated successfully');
      }),
      
      changePassword: withAsyncAction<UserStore>('UserStore', 'changePassword')(async function(currentPassword: string, newPassword: string) {
        const response = await fetch('/api/user/password', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ currentPassword, newPassword }),
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to change password');
        }
        
        toast.success('Password changed successfully');
      }),
      
      verifyEmail: withAsyncAction<UserStore>('UserStore', 'verifyEmail')(async function(token: string) {
        const response = await fetch('/api/auth/verify-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to verify email');
        }
        
        set((state) => {
          if (state.user) {
            state.user.emailVerified = new Date();
          }
        });
        
        toast.success('Email verified successfully');
      }),
      
      // Address management
      fetchAddresses: withAsyncAction<UserStore>('UserStore', 'fetchAddresses')(async function() {
        if (!shouldRefetch(get().lastAddressesFetch, 10 * 60 * 1000)) {
          return;
        }
        
        const response = await fetch('/api/user/addresses');
        
        if (!response.ok) {
          throw new Error('Failed to fetch addresses');
        }
        
        const addresses = await response.json();
        
        set((state) => {
          state.addresses = addresses;
          state.lastAddressesFetch = Date.now();
        });
      }),
      
      addAddress: withAsyncAction<UserStore>('UserStore', 'addAddress')(async function(address: Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) {
        const response = await fetch('/api/user/addresses', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(address),
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to add address');
        }
        
        const newAddress = await response.json();
        
        set((state) => {
          state.addresses.push(newAddress);
        });
        
        toast.success('Address added successfully');
      }),
      
      updateAddress: withAsyncAction<UserStore>('UserStore', 'updateAddress')(async function(id: string, data: Partial<Address>) {
        const response = await fetch(`/api/user/addresses/${id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to update address');
        }
        
        const updatedAddress = await response.json();
        
        set((state) => {
          const index = state.addresses.findIndex(addr => addr.id === id);
          if (index >= 0) {
            state.addresses[index] = updatedAddress;
          }
        });
        
        toast.success('Address updated successfully');
      }),
      
      deleteAddress: withAsyncAction<UserStore>('UserStore', 'deleteAddress')(async function(id: string) {
        const response = await fetch(`/api/user/addresses/${id}`, {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('Failed to delete address');
        }
        
        set((state) => {
          state.addresses = state.addresses.filter(addr => addr.id !== id);
        });
        
        toast.success('Address deleted successfully');
      }),
      
      setDefaultAddress: withAsyncAction<UserStore>('UserStore', 'setDefaultAddress')(async function(id: string) {
        const response = await fetch(`/api/user/addresses/${id}/default`, {
          method: 'PUT',
        });
        
        if (!response.ok) {
          throw new Error('Failed to set default address');
        }
        
        set((state) => {
          state.addresses = state.addresses.map(addr => ({
            ...addr,
            isDefault: addr.id === id,
          }));
        });
        
        toast.success('Default address updated');
      }),
      
      // Wishlist management  
      fetchWishlist: withAsyncAction<UserStore>('UserStore', 'fetchWishlist')(async function() {
        if (!shouldRefetch(get().lastWishlistFetch, 5 * 60 * 1000)) {
          return;
        }
        
        const response = await fetch('/api/user/wishlist');
        
        if (!response.ok) {
          throw new Error('Failed to fetch wishlist');
        }
        
        const wishlist = await response.json();
        const productIds = wishlist.map((item: any) => item.productId);
        
        set((state) => {
          state.wishlistItems = productIds;
          state.lastWishlistFetch = Date.now();
        });
      }),
      
      addToWishlist: withAsyncAction<UserStore>('UserStore', 'addToWishlist')(async function(productId: string) {
        // Optimistic update
        set((state) => {
          if (!state.wishlistItems.includes(productId)) {
            state.wishlistItems.push(productId);
          }
        });
        
        try {
          const response = await fetch('/api/user/wishlist', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ productId }),
          });
          
          if (!response.ok) {
            throw new Error('Failed to add to wishlist');
          }
          
          toast.success('Added to wishlist');
        } catch (error) {
          // Revert optimistic update
          set((state) => {
            state.wishlistItems = state.wishlistItems.filter(id => id !== productId);
          });
          throw error;
        }
      }),
      
      removeFromWishlist: withAsyncAction<UserStore>('UserStore', 'removeFromWishlist')(async function(productId: string) {
        // Optimistic update
        set((state) => {
          state.wishlistItems = state.wishlistItems.filter(id => id !== productId);
        });
        
        try {
          const response = await fetch(`/api/user/wishlist/${productId}`, {
            method: 'DELETE',
          });
          
          if (!response.ok) {
            throw new Error('Failed to remove from wishlist');
          }
          
          toast.success('Removed from wishlist');
        } catch (error) {
          // Revert optimistic update
          set((state) => {
            if (!state.wishlistItems.includes(productId)) {
              state.wishlistItems.push(productId);
            }
          });
          throw error;
        }
      }),
      
      clearWishlist: withAsyncAction<UserStore>('UserStore', 'clearWishlist')(async function() {
        const response = await fetch('/api/user/wishlist', {
          method: 'DELETE',
        });
        
        if (!response.ok) {
          throw new Error('Failed to clear wishlist');
        }
        
        set((state) => {
          state.wishlistItems = [];
        });
        
        toast.success('Wishlist cleared');
      }),
      
      isInWishlist: (productId: string) => {
        return get().wishlistItems.includes(productId);
      },
      
      // Preferences
      updatePreferences: withAsyncAction<UserStore>('UserStore', 'updatePreferences')(async function(preferences: Partial<UserPreferences>) {
        const response = await fetch('/api/user/preferences', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(preferences),
        });
        
        if (!response.ok) {
          throw new Error('Failed to update preferences');
        }
        
        const updatedPreferences = await response.json();
        
        set((state) => {
          state.preferences = updatedPreferences;
        });
        
        toast.success('Preferences updated');
      }),
      
      resetPreferences: () => {
        set((state) => {
          state.preferences = defaultPreferences;
        });
      },
      
      // Profile utilities
      calculateProfileCompletion: () => {
        const user = get().user;
        set((state) => {
          state.profileMetrics = calculateProfileCompletion(user);
        });
      },
      
      getDefaultAddress: () => {
        return get().addresses.find(addr => addr.isDefault) || null;
      },
      
      // Security
      enable2FA: withAsyncAction<UserStore>('UserStore', 'enable2FA')(async function(phone: string) {
        const response = await fetch('/api/user/2fa/enable', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ phone }),
        });
        
        if (!response.ok) {
          throw new Error('Failed to enable 2FA');
        }
        
        toast.success('2FA enabled successfully');
      }),
      
      disable2FA: withAsyncAction<UserStore>('UserStore', 'disable2FA')(async function() {
        const response = await fetch('/api/user/2fa/disable', {
          method: 'POST',
        });
        
        if (!response.ok) {
          throw new Error('Failed to disable 2FA');
        }
        
        toast.success('2FA disabled');
      }),
      
      updateSecuritySettings: withAsyncAction<UserStore>('UserStore', 'updateSecuritySettings')(async function(settings: any) {
        const response = await fetch('/api/user/security', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(settings),
        });
        
        if (!response.ok) {
          throw new Error('Failed to update security settings');
        }
        
        toast.success('Security settings updated');
      }),
      
      // Data management
      requestDataExport: withAsyncAction<UserStore>('UserStore', 'requestDataExport')(async function() {
        const response = await fetch('/api/user/data-export', {
          method: 'POST',
        });
        
        if (!response.ok) {
          throw new Error('Failed to request data export');
        }
        
        toast.success('Data export requested. You will receive an email when ready.');
      }),
      
      deleteAccount: withAsyncAction<UserStore>('UserStore', 'deleteAccount')(async function(password: string) {
        const response = await fetch('/api/user/delete-account', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ password }),
        });
        
        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Failed to delete account');
        }
        
        // Clear all user data
        get().reset();
        
        toast.success('Account deleted successfully');
      }),
      
      // Session management
      updateLastActivity: () => {
        set((state) => {
          if (state.sessionData) {
            state.sessionData.lastActivity = Date.now();
          }
        });
      },
      
      extendSession: withAsyncAction<UserStore>('UserStore', 'extendSession')(async function() {
        const response = await fetch('/api/auth/extend-session', {
          method: 'POST',
        });
        
        if (!response.ok) {
          throw new Error('Failed to extend session');
        }
        
        get().updateLastActivity();
      }),
    }),
    {
      name: 'user-store',
      version: 1,
      partialize: (state) => ({
        preferences: state.preferences,
        wishlistItems: state.wishlistItems,
      }),
    }
  )
);

/**
 * User store selectors for optimized re-renders
 */
export const userSelectors = {
  user: (state: UserStore) => state.user,
  isAuthenticated: (state: UserStore) => state.isAuthenticated,
  loading: (state: UserStore) => state.loading,
  error: (state: UserStore) => state.error,
  addresses: (state: UserStore) => state.addresses,
  wishlistItems: (state: UserStore) => state.wishlistItems,
  preferences: (state: UserStore) => state.preferences,
  profileMetrics: (state: UserStore) => state.profileMetrics,
  defaultAddress: (state: UserStore) => state.addresses.find(addr => addr.isDefault) || null,
  isProfileComplete: (state: UserStore) => state.profileMetrics.isComplete,
  sessionData: (state: UserStore) => state.sessionData,
};

/**
 * Export store instance for external use
 */
export const userStore = useUserStore.getState();