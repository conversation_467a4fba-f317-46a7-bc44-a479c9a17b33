import { StateCreator } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { devtools } from 'zustand/middleware';

// Import React for HOC
import React from 'react';

/**
 * Base store interface with common state patterns
 */
export interface BaseState {
  // Loading states
  loading: boolean;
  error: string | null;
  
  // Timestamps
  lastFetch?: number;
  lastUpdate?: number;
}

/**
 * Base actions interface
 */
export interface BaseActions {
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Reset functionality
  reset: () => void;
}

/**
 * Combined base store interface
 */
export interface BaseStore extends BaseState, BaseActions {}

/**
 * Create base store implementation
 */
export function createBaseStore<T extends BaseStore>(
  name: string,
  initialState: Omit<T, keyof BaseActions>,
  customActions?: (set: any, get: any) => Partial<T>
): StateCreator<T, [["zustand/immer", never], ["zustand/subscribeWithSelector", never], ["zustand/devtools", never]], [], T> {
  return immer(
    subscribeWithSelector(
      devtools(
        (set, get) => ({
          // Initial state
          ...initialState,
          loading: false,
          error: null,
          
          // Base actions
          setLoading: (loading: boolean) => 
            set((state) => {
              state.loading = loading;
            }),
            
          setError: (error: string | null) => 
            set((state) => {
              state.error = error;
              state.loading = false;
            }),
            
          clearError: () => 
            set((state) => {
              state.error = null;
            }),
            
          reset: () => 
            set((state) => {
              // Reset to initial state
              Object.keys(state).forEach(key => {
                if (key in initialState) {
                  (state as any)[key] = (initialState as any)[key];
                }
              });
              state.loading = false;
              state.error = null;
              state.lastFetch = undefined;
              state.lastUpdate = undefined;
            }),
            
          // Custom actions
          ...customActions?.(set, get),
        } as T),
        { name }
      )
    )
  );
}

/**
 * Async action wrapper with error handling
 */
export function withAsyncAction<T extends BaseStore>(
  storeName: string,
  actionName: string
) {
  return function<Args extends any[], Return>(
    asyncFunction: (...args: Args) => Promise<Return>
  ) {
    return async function(this: T, ...args: Args): Promise<Return | null> {
      try {
        this.setLoading(true);
        this.clearError();
        
        const startTime = Date.now();
        const result = await asyncFunction.apply(this, args);
        
        // Update timestamps
        if ('lastUpdate' in this) {
          (this as any).lastUpdate = Date.now();
        }
        
        // Log performance in development
        if (process.env.NODE_ENV === 'development') {
          const duration = Date.now() - startTime;
          console.log(`🏪 ${storeName}.${actionName} completed in ${duration}ms`);
        }
        
        this.setLoading(false);
        return result;
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        
        console.error(`❌ ${storeName}.${actionName} failed:`, error);
        this.setError(errorMessage);
        
        return null;
      }
    };
  };
}

/**
 * Cache invalidation helper
 */
export function shouldRefetch(lastFetch?: number, ttl: number = 5 * 60 * 1000): boolean {
  if (!lastFetch) return true;
  return Date.now() - lastFetch > ttl;
}

/**
 * Optimistic update helper
 */
export function withOptimisticUpdate<T extends BaseStore, Args extends any[]>(
  optimisticUpdate: (state: T, ...args: Args) => void,
  revertUpdate: (state: T) => void,
  serverUpdate: (...args: Args) => Promise<any>
) {
  return async function(this: T, ...args: Args) {
    // Apply optimistic update
    optimisticUpdate(this as T, ...args);
    
    try {
      // Perform server update
      const result = await serverUpdate.apply(this, args);
      
      // Update with server response if needed
      if (result && typeof result === 'object') {
        Object.assign(this, result);
      }
      
      return result;
    } catch (error) {
      // Revert optimistic update on error
      revertUpdate(this as T);
      throw error;
    }
  };
}

/**
 * Batch update helper to prevent unnecessary re-renders
 */
export function batchUpdate<T>(
  set: (updater: (state: T) => void) => void,
  updates: Array<(state: T) => void>
) {
  set((state) => {
    updates.forEach(update => update(state));
  });
}

/**
 * Create store with persistence
 */
export interface PersistOptions {
  name: string;
  version?: number;
  migrate?: (persistedState: any, version: number) => any;
  partialize?: (state: any) => any;
  storage?: {
    getItem: (name: string) => string | null | Promise<string | null>;
    setItem: (name: string, value: string) => void | Promise<void>;
    removeItem: (name: string) => void | Promise<void>;
  };
}

/**
 * Default storage implementation using localStorage
 */
export const defaultStorage = {
  getItem: (name: string): string | null => {
    if (typeof window === 'undefined') return null;
    try {
      return localStorage.getItem(name);
    } catch {
      return null;
    }
  },
  setItem: (name: string, value: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(name, value);
    } catch {
      // Silently fail if localStorage is not available
    }
  },
  removeItem: (name: string): void => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.removeItem(name);
    } catch {
      // Silently fail if localStorage is not available
    }
  },
};

/**
 * Create persisted store
 */
export function createPersistedStore<T extends BaseStore>(
  name: string,
  initialState: Omit<T, keyof BaseActions>,
  customActions: (set: any, get: any) => Partial<T>,
  persistOptions: PersistOptions
) {
  const storage = persistOptions.storage || defaultStorage;
  const version = persistOptions.version || 1;
  
  // Load persisted state
  const loadPersistedState = (): Partial<T> | null => {
    try {
      const item = storage.getItem(persistOptions.name);
      if (!item) return null;
      
      const parsed = JSON.parse(item);
      
      // Handle version migration
      if (parsed.version !== version && persistOptions.migrate) {
        const migrated = persistOptions.migrate(parsed.state, parsed.version || 0);
        return migrated;
      }
      
      return parsed.version === version ? parsed.state : null;
    } catch {
      return null;
    }
  };
  
  // Save state to storage
  const saveState = (state: T) => {
    try {
      const stateToSave = persistOptions.partialize ? persistOptions.partialize(state) : state;
      const serialized = JSON.stringify({
        state: stateToSave,
        version,
        timestamp: Date.now(),
      });
      storage.setItem(persistOptions.name, serialized);
    } catch (error) {
      console.warn(`Failed to persist store ${name}:`, error);
    }
  };
  
  // Load initial state
  const persistedState = loadPersistedState();
  const mergedInitialState = persistedState 
    ? { ...initialState, ...persistedState }
    : initialState;
  
  return createBaseStore<T>(
    name,
    mergedInitialState,
    (set, get) => {
      const actions = customActions(set, get);
      
      // Wrap set to trigger persistence
      const persistentSet = (updater: any) => {
        set(updater);
        // Save state after update (debounced to avoid excessive writes)
        const state = get();
        setTimeout(() => saveState(state), 100);
      };
      
      return {
        ...actions,
        // Override actions to use persistent set
        ...(Object.keys(actions).reduce((acc, key) => {
          const action = (actions as any)[key];
          if (typeof action === 'function') {
            acc[key] = (...args: any[]) => {
              const result = action.call({ ...get(), ...actions }, ...args);
              if (result instanceof Promise) {
                return result.then((res) => {
                  saveState(get());
                  return res;
                });
              }
              saveState(get());
              return result;
            };
          } else {
            acc[key] = action;
          }
          return acc;
        }, {} as any)),
      };
    }
  );
}

/**
 * Store subscription helper
 */
export function createStoreSubscription<T>(
  store: { subscribe: (listener: (state: T, prevState: T) => void) => () => void },
  selector: (state: T) => any,
  callback: (selected: any, prevSelected: any) => void
) {
  let prevSelected = selector(store as any);
  
  return store.subscribe((state, prevState) => {
    const selected = selector(state);
    const prevSelectedValue = selector(prevState);
    
    if (selected !== prevSelectedValue) {
      callback(selected, prevSelectedValue);
      prevSelected = selected;
    }
  });
}