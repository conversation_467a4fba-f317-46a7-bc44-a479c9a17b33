"use client";

import React, { useState, useEffect } from "react";
import { DynamicForm } from "@/components/shared/forms";
import { FormFieldConfig } from "@/types/core/ui";
import { AttributeType, AttributeFormData, AttributeValueFormData } from "@/types/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Plus, Trash2, GripVertical } from "lucide-react";
import { toast } from "sonner";

const ATTRIBUTE_TYPE_OPTIONS = [
  { value: 'TEXT', label: 'Text', description: 'Single line text input' },
  { value: 'NUMBER', label: 'Number', description: 'Numeric input' },
  { value: 'SELECT', label: 'Select', description: 'Single selection dropdown' },
  { value: 'MULTI_SELECT', label: 'Multi Select', description: 'Multiple selection dropdown' },
  { value: 'BOOLEAN', label: 'Boolean', description: 'Yes/No checkbox' },
  { value: 'COLOR', label: 'Color', description: 'Color picker' },
  { value: 'IMAGE', label: 'Image', description: 'Image upload' },
];

interface AttributeFormProps {
  initialData?: Partial<AttributeFormData>;
  onSubmit: (data: AttributeFormData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  mode?: "create" | "edit";
}

export function AttributeFormRefactored({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = "create",
}: AttributeFormProps) {
  const [values, setValues] = useState<AttributeValueFormData[]>([]);
  const [currentFormData, setCurrentFormData] = useState<Record<string, any>>({});

  const formFields: FormFieldConfig[] = [
    {
      name: 'name',
      label: 'Attribute Name',
      type: 'text',
      required: true,
      placeholder: 'Enter attribute name',
      grid: { span: 1 },
    },
    {
      name: 'slug',
      label: 'Slug',
      type: 'text',
      required: true,
      placeholder: 'auto-generated-slug',
      helperText: 'URL-friendly version of the name',
      grid: { span: 1 },
    },
    {
      name: 'type',
      label: 'Attribute Type',
      type: 'select',
      required: true,
      options: ATTRIBUTE_TYPE_OPTIONS,
      grid: { span: 2 },
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Describe this attribute...',
      grid: { span: 2 },
    },
    {
      name: 'isRequired',
      label: 'Required Field',
      type: 'switch',
      helperText: 'Make this attribute mandatory for products',
      grid: { span: 1 },
    },
    {
      name: 'isFilterable',
      label: 'Use in Filters',
      type: 'switch',
      helperText: 'Allow customers to filter products by this attribute',
      grid: { span: 1 },
    },
    {
      name: 'sortOrder',
      label: 'Sort Order',
      type: 'number',
      placeholder: '0',
      helperText: 'Lower numbers appear first',
      grid: { span: 2 },
    },
  ];

  useEffect(() => {
    if (initialData?.values) {
      setValues(initialData.values);
    }
  }, [initialData]);

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .trim();
  };

  const addValue = () => {
    const newValue: AttributeValueFormData = {
      id: `temp-${Date.now()}`,
      value: '',
      slug: '',
      sortOrder: values.length,
      attributeId: '',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setValues([...values, newValue]);
  };

  const updateValue = (index: number, field: keyof AttributeValueFormData, value: any) => {
    const newValues = [...values];
    newValues[index] = { ...newValues[index], [field]: value };
    
    // Auto-generate slug for value
    if (field === 'value' && value) {
      newValues[index].slug = generateSlug(value);
    }
    
    setValues(newValues);
  };

  const removeValue = (index: number) => {
    setValues(values.filter((_, i) => i !== index));
  };

  const validateValues = (type: string) => {
    if (['SELECT', 'MULTI_SELECT', 'COLOR'].includes(type)) {
      return values.length > 0;
    }
    return true;
  };

  const handleFormSubmit = async (formData: Record<string, any>) => {
    if (!validateValues(formData.type)) {
      toast.error('Please add at least one value for this attribute type');
      return;
    }

    try {
      const submitData: AttributeFormData = {
        ...formData,
        values,
      } as AttributeFormData;
      
      await onSubmit(submitData);
      toast.success(
        mode === "create"
          ? "Attribute created successfully"
          : "Attribute updated successfully"
      );
    } catch (error) {
      toast.error("Failed to save attribute");
      console.error("Error saving attribute:", error);
    }
  };

  const handleFormDataChange = (data: Record<string, any>) => {
    // Auto-generate slug if name changes and we're creating
    if (data.name && data.name !== currentFormData.name && mode === "create") {
      data.slug = generateSlug(data.name);
    }
    setCurrentFormData(data);
  };

  const needsValues = (type: string) => {
    return ['SELECT', 'MULTI_SELECT', 'COLOR'].includes(type);
  };

  return (
    <div className="space-y-6">
      <DynamicForm
        fields={formFields}
        initialData={{
          name: '',
          slug: '',
          description: '',
          type: 'TEXT',
          isRequired: false,
          isFilterable: true,
          sortOrder: 0,
          ...initialData,
        }}
        onSubmit={handleFormSubmit}
        loading={loading}
        submitLabel={mode === 'create' ? 'Create Attribute' : 'Update Attribute'}
        showCancel
        onCancel={onCancel}
        title={mode === 'create' ? 'Create New Attribute' : 'Edit Attribute'}
        variant="card"
      />

      {/* Attribute Values Section */}
      {(needsValues(currentFormData.type || initialData?.type || '') || values.length > 0) && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Attribute Values</CardTitle>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addValue}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Value
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {values.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No values added yet. Click "Add Value" to get started.
              </div>
            ) : (
              <div className="space-y-3">
                {values.map((value, index) => (
                  <Card key={value.id || index} className="p-4">
                    <div className="flex items-start space-x-4">
                      <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Value</label>
                          <Input
                            value={value.value}
                            onChange={(e) => updateValue(index, 'value', e.target.value)}
                            placeholder="Enter value"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Slug</label>
                          <Input
                            value={value.slug}
                            onChange={(e) => updateValue(index, 'slug', e.target.value)}
                            placeholder="auto-generated"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium">Sort Order</label>
                          <Input
                            type="number"
                            value={value.sortOrder}
                            onChange={(e) => updateValue(index, 'sortOrder', parseInt(e.target.value) || 0)}
                            placeholder="0"
                          />
                        </div>
                        {currentFormData.type === 'COLOR' && (
                          <div className="space-y-2 md:col-span-3">
                            <label className="text-sm font-medium">Color</label>
                            <div className="flex space-x-2">
                              <Input
                                type="color"
                                value={value.color || "#000000"}
                                onChange={(e) => updateValue(index, 'color', e.target.value)}
                                className="w-16"
                              />
                              <Input
                                value={value.color || ""}
                                onChange={(e) => updateValue(index, 'color', e.target.value)}
                                placeholder="#000000"
                                className="flex-1"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col space-y-2">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="text-muted-foreground hover:text-foreground cursor-move"
                        >
                          <GripVertical className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeValue(index)}
                          className="text-destructive hover:text-destructive/80"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
            
            {values.length > 0 && (
              <div className="mt-4 flex items-center justify-between">
                <Badge variant="secondary">
                  {values.length} value{values.length !== 1 ? "s" : ""}
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}