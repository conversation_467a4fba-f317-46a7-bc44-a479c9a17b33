import React from 'react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormFieldConfig } from '@/types/core/ui';

interface FormFieldProps {
  config: FormFieldConfig;
  value?: any;
  onChange: (name: string, value: any) => void;
  errors?: Record<string, string>;
}

export function FormField({ config, value, onChange, errors }: FormFieldProps) {
  const { name, label, type, required, placeholder, helperText, options } = config;
  const error = errors?.[name];
  
  const handleChange = (newValue: any) => {
    onChange(name, newValue);
  };

  const renderField = () => {
    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
        return (
          <Input
            name={name}
            type={type}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={placeholder}
            required={required}
            error={error}
          />
        );

      case 'textarea':
        return (
          <Textarea
            name={name}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            placeholder={placeholder}
            required={required}
            error={error}
          />
        );

      case 'select':
        return (
          <Select value={value || ''} onValueChange={handleChange}>
            <SelectTrigger>
              <SelectValue placeholder={placeholder || `Select ${label}`} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.icon && <span className="mr-2">{option.icon}</span>}
                  <div>
                    <div>{option.label}</div>
                    {option.description && (
                      <div className="text-sm text-muted-foreground">
                        {option.description}
                      </div>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiselect':
        // TODO: Implement multi-select component
        return <div>Multi-select not implemented yet</div>;

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={name}
              checked={value || false}
              onCheckedChange={handleChange}
            />
            <Label htmlFor={name}>{label}</Label>
          </div>
        );

      case 'switch':
        return (
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor={name}>{label}</Label>
              {helperText && (
                <p className="text-sm text-muted-foreground">{helperText}</p>
              )}
            </div>
            <Switch
              id={name}
              checked={value || false}
              onCheckedChange={handleChange}
            />
          </div>
        );

      case 'date':
      case 'datetime':
        return (
          <Input
            name={name}
            type={type === 'date' ? 'date' : 'datetime-local'}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            required={required}
            error={error}
          />
        );

      case 'file':
        return (
          <Input
            name={name}
            type="file"
            onChange={(e) => handleChange(e.target.files?.[0])}
            required={required}
            error={error}
          />
        );

      default:
        return <div>Unknown field type: {type}</div>;
    }
  };

  if (type === 'checkbox' || type === 'switch') {
    return (
      <div className="space-y-2">
        {renderField()}
        {error && <p className="text-sm text-destructive">{error}</p>}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={name}>
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </Label>
      {renderField()}
      {helperText && !error && (
        <p className="text-sm text-muted-foreground">{helperText}</p>
      )}
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
}