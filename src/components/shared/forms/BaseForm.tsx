import React from 'react';
import { FormProps, BaseComponentProps } from '@/types/core/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface BaseFormProps extends BaseComponentProps, FormProps {
  title?: string;
  subtitle?: string;
  submitLabel?: string;
  cancelLabel?: string;
  showCancel?: boolean;
  onCancel?: () => void;
  actions?: React.ReactNode;
  variant?: 'default' | 'card';
}

export function BaseForm({
  title,
  subtitle,
  submitLabel = 'Save',
  cancelLabel = 'Cancel',
  showCancel = true,
  onCancel,
  onSubmit,
  loading = false,
  disabled = false,
  children,
  actions,
  variant = 'default',
  className = '',
  ...props
}: BaseFormProps) {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading || disabled) return;
    
    const formData = new FormData(e.target as HTMLFormElement);
    const data = Object.fromEntries(formData.entries());
    await onSubmit(data);
  };

  const formContent = (
    <form onSubmit={handleSubmit} className={`space-y-6 ${className}`} {...props}>
      {children}
      
      <div className="flex items-center justify-between pt-6 border-t">
        <div className="flex items-center space-x-2">
          {actions}
        </div>
        
        <div className="flex items-center space-x-2">
          {showCancel && onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              {cancelLabel}
            </Button>
          )}
          
          <Button
            type="submit"
            loading={loading}
            disabled={disabled}
          >
            {submitLabel}
          </Button>
        </div>
      </div>
    </form>
  );

  if (variant === 'card') {
    return (
      <Card>
        {(title || subtitle) && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
          </CardHeader>
        )}
        <CardContent>
          {formContent}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {(title || subtitle) && (
        <div className="space-y-2">
          {title && <h2 className="text-2xl font-bold">{title}</h2>}
          {subtitle && <p className="text-muted-foreground">{subtitle}</p>}
        </div>
      )}
      {formContent}
    </div>
  );
}