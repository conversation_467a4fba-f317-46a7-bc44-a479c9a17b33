import React, { useState, useEffect } from 'react';
import { DynamicFormProps } from '@/types/core/ui';
import { BaseForm } from './BaseForm';
import { FormField } from './FormField';

export function DynamicForm({
  fields,
  initialData = {},
  onSubmit,
  loading = false,
  submitLabel = 'Save',
  resetLabel = 'Reset',
  showReset = false,
  className,
  ...props
}: DynamicFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    setFormData(initialData);
  }, [initialData]);

  const handleFieldChange = (name: string, value: any) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is changed
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    fields.forEach(field => {
      const value = formData[field.name];
      
      if (field.required && (!value || (typeof value === 'string' && !value.trim()))) {
        newErrors[field.name] = `${field.label} is required`;
      }
      
      // Add custom validation if provided
      if (field.validation && value) {
        try {
          field.validation.parse(value);
        } catch (error: any) {
          newErrors[field.name] = error.message;
        }
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (data: Record<string, any>) => {
    if (validateForm()) {
      await onSubmit(formData);
    }
  };

  const handleReset = () => {
    setFormData(initialData);
    setErrors({});
  };

  const shouldShowField = (field: any) => {
    if (!field.conditional) return true;
    
    const { field: conditionField, value: conditionValue, operator = 'equals' } = field.conditional;
    const currentValue = formData[conditionField];
    
    switch (operator) {
      case 'equals':
        return currentValue === conditionValue;
      case 'not_equals':
        return currentValue !== conditionValue;
      case 'contains':
        return Array.isArray(currentValue) ? currentValue.includes(conditionValue) : false;
      case 'not_contains':
        return Array.isArray(currentValue) ? !currentValue.includes(conditionValue) : true;
      default:
        return true;
    }
  };

  const visibleFields = fields.filter(shouldShowField);

  return (
    <BaseForm
      onSubmit={handleSubmit}
      loading={loading}
      submitLabel={submitLabel}
      actions={
        showReset && (
          <button
            type="button"
            onClick={handleReset}
            className="text-sm text-muted-foreground hover:text-foreground"
          >
            {resetLabel}
          </button>
        )
      }
      className={className}
      {...props}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {visibleFields.map((field) => (
          <div
            key={field.name}
            className={`
              ${field.grid?.span ? `md:col-span-${field.grid.span}` : ''}
              ${field.grid?.offset ? `md:col-start-${field.grid.offset + 1}` : ''}
            `}
          >
            <FormField
              config={field}
              value={formData[field.name]}
              onChange={handleFieldChange}
              errors={errors}
            />
          </div>
        ))}
      </div>
    </BaseForm>
  );
}