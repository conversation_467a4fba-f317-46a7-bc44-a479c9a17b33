"use client";

import {
  AttributeForm,
  AttributeValueManager,
} from "@/components/admin/attributes";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  Attribute,
  ATTRIBUTE_TYPE_LABELS,
  AttributeFormData,
  AttributeValue,
} from "@/types/attribute";
import {
  AlertCircle,
  ArrowLeft,
  Eye,
  Loader2,
  Package,
  Settings,
  Trash2,
} from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export default function EditAttributePage() {
  const router = useRouter();
  const params = useParams();
  const attributeId = params.id as string;

  const [attribute, setAttribute] = useState<Attribute | null>(null);
  const [values, setValues] = useState<AttributeValue[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Fetch attribute data
  const fetchAttribute = async () => {
    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}`);
      if (response.ok) {
        const data = await response.json();
        setAttribute(data);
        setValues(data.values || []);
      } else {
        toast.error("Không tìm thấy thuộc tính");
        router.push("/admin/attributes");
      }
    } catch (error) {
      console.error("Error fetching attribute:", error);
      toast.error("Có lỗi xảy ra khi tải thông tin thuộc tính");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (attributeId) {
      fetchAttribute();
    }
  }, [attributeId]);

  const handleSubmit = async (formData: AttributeFormData) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const updatedAttribute = await response.json();
        setAttribute(updatedAttribute);
        toast.success("Cập nhật thuộc tính thành công");
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi cập nhật thuộc tính");
      }
    } catch (error) {
      console.error("Error updating attribute:", error);
      toast.error("Có lỗi xảy ra khi cập nhật thuộc tính");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    setDeleting(true);
    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Xóa thuộc tính thành công");
        router.push("/admin/attributes");
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi xóa thuộc tính");
      }
    } catch (error) {
      console.error("Error deleting attribute:", error);
      toast.error("Có lỗi xảy ra khi xóa thuộc tính");
    } finally {
      setDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/attributes");
  };

  const handleValuesChange = (newValues: AttributeValue[]) => {
    setValues(newValues);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Đang tải thông tin thuộc tính...</p>
        </div>
      </div>
    );
  }

  if (!attribute) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
        <h2 className="text-xl font-semibold mb-2">
          Không tìm thấy thuộc tính
        </h2>
        <p className="text-muted-foreground mb-4">
          Thuộc tính bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.
        </p>
        <Link href="/admin/attributes">
          <Button>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại danh sách
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/attributes">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Quay lại
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Chỉnh sửa thuộc tính</h1>
            <p className="text-muted-foreground">
              Cập nhật thông tin và quản lý giá trị thuộc tính
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Link href={`/admin/attributes/${attributeId}`}>
            <Button variant="outline" size="sm">
              <Eye className="h-4 w-4 mr-2" />
              Xem chi tiết
            </Button>
          </Link>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setShowDeleteDialog(true)}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Xóa
          </Button>
        </div>
      </div>

      {/* Attribute Info */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {attribute.name}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {ATTRIBUTE_TYPE_LABELS[attribute.type]}
              </Badge>
              {attribute.isRequired && (
                <Badge variant="destructive">Bắt buộc</Badge>
              )}
              {attribute.isFilterable && (
                <Badge variant="secondary">Có thể lọc</Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Slug:</span>
              <p className="text-muted-foreground">{attribute.slug}</p>
            </div>
            <div>
              <span className="font-medium">Số giá trị:</span>
              <p className="text-muted-foreground">
                {attribute._count?.values || 0}
              </p>
            </div>
            <div>
              <span className="font-medium">Sản phẩm sử dụng:</span>
              <p className="text-muted-foreground">
                {attribute._count?.products || 0}
              </p>
            </div>
          </div>
          {attribute.description && (
            <div className="mt-4">
              <span className="font-medium">Mô tả:</span>
              <p className="text-muted-foreground mt-1">
                {attribute.description}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Form */}
      <AttributeForm
        initialData={{
          name: attribute.name,
          slug: attribute.slug,
          description: attribute.description || "",
          type: attribute.type,
          isRequired: attribute.isRequired,
          isFilterable: attribute.isFilterable,
          sortOrder: attribute.sortOrder,
        }}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        loading={saving}
        mode="edit"
      />

      <Separator />

      {/* Values Management */}
      {["COLOR", "SIZE", "SELECT", "MULTI_SELECT"].includes(attribute.type) && (
        <AttributeValueManager
          attributeId={attribute.id}
          attributeName={attribute.name}
          values={values}
          onValuesChange={handleValuesChange}
        />
      )}

      {/* Usage Information */}
      {attribute._count?.products && attribute._count.products > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Thông tin sử dụng
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <AlertCircle className="h-4 w-4" />
              <span>
                Thuộc tính này đang được sử dụng bởi{" "}
                <strong>{attribute._count.products}</strong> sản phẩm. Việc thay
                đổi có thể ảnh hưởng đến các sản phẩm này.
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Xác nhận xóa thuộc tính</DialogTitle>
            <DialogDescription>
              Bạn có chắc chắn muốn xóa thuộc tính "{attribute.name}"?
              {attribute._count?.products && attribute._count.products > 0 && (
                <span className="block mt-2 text-red-600">
                  Cảnh báo: Thuộc tính này đang được sử dụng bởi{" "}
                  {attribute._count.products} sản phẩm. Việc xóa sẽ ảnh hưởng
                  đến các sản phẩm này.
                </span>
              )}
              {attribute._count?.values && attribute._count.values > 0 && (
                <span className="block mt-1 text-orange-600">
                  Thuộc tính này có {attribute._count.values} giá trị sẽ bị xóa
                  cùng.
                </span>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={deleting}
            >
              Hủy
            </Button>
            <Button
              onClick={handleDelete}
              variant="destructive"
              disabled={deleting}
            >
              {deleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Đang xóa...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Xóa thuộc tính
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
